#!/usr/bin/env python3
"""
港股通数据监控脚本

定期检查港股通数据的更新情况，特别关注数据缺失的日期。
"""

import pandas as pd
import time
from datetime import datetime, timedelta

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    print("❌ AKShare not available")
    exit(1)

def check_hsgt_data_status(stock_code="00001", stock_name="长和"):
    """检查指定股票的港股通数据状态"""
    
    print(f"🔍 Checking HSGT data for {stock_code} ({stock_name})")
    print(f"⏰ Check time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)
    
    try:
        # 获取港股通数据
        hsgt_data = ak.stock_hsgt_individual_em(symbol=stock_code)
        
        if hsgt_data is None or hsgt_data.empty:
            print("❌ No HSGT data available")
            return
        
        # 获取最新数据
        hsgt_data_sorted = hsgt_data.sort_values('持股日期', ascending=False)
        latest_record = hsgt_data_sorted.iloc[0]
        
        latest_date = latest_record['持股日期']
        latest_shares = latest_record['持股数量']
        latest_value = latest_record['持股市值']
        latest_ratio = latest_record['持股数量占A股百分比']
        
        print(f"📅 Latest HSGT date: {latest_date}")
        print(f"📊 Holding shares: {latest_shares:,.0f}")
        print(f"💰 Holding value: {latest_value:,.0f} HKD")
        print(f"📈 Holding ratio: {latest_ratio:.2f}%")
        
        # 检查今天的数据
        today = datetime.now().strftime('%Y-%m-%d')
        today_data = hsgt_data[hsgt_data['持股日期'] == today]
        
        if not today_data.empty:
            print(f"✅ Today's data ({today}) is available!")
        else:
            print(f"⏳ Today's data ({today}) not yet available")
            
            # 计算数据延迟
            latest_date_obj = datetime.strptime(latest_date, '%Y-%m-%d')
            today_obj = datetime.now()
            delay_days = (today_obj - latest_date_obj).days
            
            if delay_days == 1:
                print(f"📊 Data is 1 day behind (normal delay)")
            elif delay_days > 1:
                print(f"⚠️  Data is {delay_days} days behind")
        
        # 显示最近5天的数据
        print(f"\n📋 Recent 5 records:")
        for i, (_, row) in enumerate(hsgt_data_sorted.head(5).iterrows()):
            date = row['持股日期']
            shares = row['持股数量']
            value = row['持股市值']
            print(f"  {i+1}. {date}: {shares:>12,.0f} shares, {value:>15,.0f} HKD")
        
        return latest_date
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def check_multiple_stocks():
    """检查多只股票的港股通数据状态"""
    
    stocks = [
        ("00001", "长和"),
        ("00005", "汇丰控股"), 
        ("00700", "腾讯控股"),
        ("01810", "小米集团-W"),
        ("02269", "药明生物")
    ]
    
    print("🔍 Multi-Stock HSGT Data Status Check")
    print("=" * 60)
    
    results = {}
    
    for stock_code, stock_name in stocks:
        print(f"\n📊 {stock_code} ({stock_name})")
        print("-" * 30)
        
        try:
            hsgt_data = ak.stock_hsgt_individual_em(symbol=stock_code)
            
            if hsgt_data is not None and not hsgt_data.empty:
                latest_date = hsgt_data.sort_values('持股日期', ascending=False).iloc[0]['持股日期']
                results[stock_code] = latest_date
                print(f"✅ Latest: {latest_date}")
            else:
                results[stock_code] = "No data"
                print(f"❌ No data")
                
        except Exception as e:
            results[stock_code] = f"Error: {e}"
            print(f"❌ Error: {e}")
        
        # 添加延迟避免API限制
        time.sleep(1)
    
    # 汇总结果
    print(f"\n📋 Summary:")
    print("-" * 40)
    for stock_code, latest_date in results.items():
        stock_name = next(name for code, name in stocks if code == stock_code)
        print(f"{stock_code} ({stock_name}): {latest_date}")
    
    return results

def main():
    """主函数"""
    print("🔍 HSGT Data Monitor")
    print("=" * 30)
    
    # 检查单只股票（长和）
    latest_date = check_hsgt_data_status()
    
    print("\n" + "=" * 60)
    
    # 检查多只股票
    results = check_multiple_stocks()
    
    # 检查数据一致性
    print(f"\n🔄 Data Consistency Check:")
    print("-" * 30)
    
    unique_dates = set(date for date in results.values() 
                      if date not in ["No data"] and not date.startswith("Error"))
    
    if len(unique_dates) <= 1:
        print("✅ All stocks have consistent latest dates")
    else:
        print("⚠️  Inconsistent latest dates across stocks:")
        for date in sorted(unique_dates):
            stocks_with_date = [code for code, d in results.items() if d == date]
            print(f"  {date}: {', '.join(stocks_with_date)}")
    
    print(f"\n🏁 Monitor completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
