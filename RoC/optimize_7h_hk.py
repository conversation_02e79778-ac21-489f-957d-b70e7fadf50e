#!/usr/bin/env python3
"""
Quick 7-Hour Parameter Optimization for RoC Strategy - Hong Kong Version
Optimized for speed while maintaining good parameter coverage
Adapted to optimize parameters for Hong Kong market data
"""

import sys
import os
import pandas as pd
import numpy as np
from itertools import product
from tqdm import tqdm
import json
import logging
from datetime import datetime
import warnings
import random
warnings.filterwarnings('ignore')

# Path setup
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import required modules - Hong Kong versions
from RoC.run_full_market_backtest_RoC_HK import run_backtest_with_params
from RoC.run_portfolio_backtest_RoC_HK import run_portfolio_backtest, add_hk_stock_code_prefix

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Quick7HourOptimizerHK:
    """
    Quick parameter optimizer designed to complete within 7 hours - Hong Kong Version
    Uses smart sampling and focuses on most impactful parameters
    """
    
    def __init__(self, target_hours=7):
        self.target_hours = target_hours
        self.project_root = project_root
        self.results = []
        self.best_params = None
        self.best_sharpe = -np.inf
        
        # Calculate max combinations based on time constraint
        # Assume ~2 minutes per combination (conservative estimate)
        self.max_combinations = int((target_hours * 60) / 2)
        logger.info(f"Target: {target_hours} hours, Max combinations: {self.max_combinations}")
        
        # In-sample period for optimization
        self.in_sample_start = '2010-01-01'
        self.in_sample_end = '2023-12-31'
        
        # Load market data once
        self._load_market_data()
        
    def _load_market_data(self):
        """Load Hong Kong market and benchmark data once for efficiency"""
        logger.info("Loading Hong Kong market data for optimization...")
        
        # Load Hong Kong market data
        market_data_path = os.path.join(self.project_root, 'data', 'h_shares_daily.parquet')
        self.market_data_df = pd.read_parquet(market_data_path)
        
        # Rename columns to match expected format
        self.market_data_df = self.market_data_df.rename(columns={
            'time_key': 'Date', 'stock_code': 'StockCode', 'close': 'Close',
            'open': 'Open', 'high': 'High', 'low': 'Low', 'volume': 'Volume'
        })
        self.market_data_df = add_hk_stock_code_prefix(self.market_data_df)
        
        # Load benchmark data (Hang Seng Index or create simple benchmark)
        benchmark_path = os.path.join(self.project_root, 'data', 'hsi_daily.csv')
        
        if os.path.exists(benchmark_path):
            self.benchmark_data_df = pd.read_csv(benchmark_path, index_col='Date', parse_dates=True)
            self.benchmark_data_df.rename(columns={'Close': 'Close'}, inplace=True)
        else:
            logger.warning("HSI data not found, creating simple market benchmark...")
            # Create a simple equal-weighted benchmark from available stocks
            self.market_data_df['Date'] = pd.to_datetime(self.market_data_df['Date'])
            self.benchmark_data_df = self.market_data_df.groupby('Date')['Close'].mean().to_frame()
            self.benchmark_data_df.columns = ['Close']
            self.benchmark_data_df.index.name = 'Date'
        
        logger.info("Hong Kong market data loaded successfully")
    
    def get_smart_parameter_combinations(self):
        """
        Generate smart parameter combinations focusing on most impactful parameters
        """
        # Define parameter ranges (reduced for speed)
        param_space = {
            'roc_period': [30],  # Most impactful - keep 3 values
            'roc_std_window': [750],  # Important - keep 3 values
            'roc_std_multiplier': [1.0, 1.25, 1.5, 1.75, 2.0],  # Very important - keep 4 values
            'profit_target_atr_multiplier': [1.5, 1.75, 2.0, 2.25, 2.5],  # Important - reduce to 3
            'stop_loss_atr_multiplier': [1.25, 1.5, 1.75, 2.0],  # Moderate impact - reduce to 3
            'max_hold_days': [30]  # Lower impact - reduce to 3
        }
        
        # Generate all combinations
        param_names = list(param_space.keys())
        param_values = list(param_space.values())
        all_combinations = list(product(*param_values))
        
        total_possible = len(all_combinations)
        logger.info(f"Total possible combinations: {total_possible}")
        
        if total_possible <= self.max_combinations:
            logger.info("Using all combinations")
            return [dict(zip(param_names, combo)) for combo in all_combinations]
        
        # Smart sampling strategy
        logger.info(f"Smart sampling {self.max_combinations} from {total_possible} combinations")
        
        selected_combinations = []
        
        # 1. Always include current best parameters
        current_best = {'roc_period': 30, 'roc_std_window': 750, 'roc_std_multiplier': 2.0,
                       'profit_target_atr_multiplier': 2.0, 'stop_loss_atr_multiplier': 1.5, 'max_hold_days': 30}
        selected_combinations.append(current_best)
        
        # 2. Include edge cases (extreme values)
        edge_combinations = self._get_edge_combinations(param_space, param_names)
        selected_combinations.extend(edge_combinations[:min(20, len(edge_combinations))])
        
        # 3. Random sampling for the rest
        remaining_budget = self.max_combinations - len(selected_combinations)
        if remaining_budget > 0:
            random.seed(42)  # For reproducibility
            remaining_combos = [dict(zip(param_names, combo)) for combo in all_combinations 
                              if dict(zip(param_names, combo)) not in selected_combinations]
            random_sample = random.sample(remaining_combos, min(remaining_budget, len(remaining_combos)))
            selected_combinations.extend(random_sample)
        
        logger.info(f"Selected {len(selected_combinations)} combinations for testing")
        return selected_combinations
    
    def _get_edge_combinations(self, param_space, param_names):
        """Generate edge case combinations"""
        edge_combinations = []
        base_params = {'roc_period': 30, 'roc_std_window': 750, 'roc_std_multiplier': 2.0,
                      'profit_target_atr_multiplier': 2.0, 'stop_loss_atr_multiplier': 1.5, 'max_hold_days': 30}
        
        # For each parameter, create combinations with min and max values
        for param_name in param_names:
            values = param_space[param_name]
            min_val, max_val = min(values), max(values)
            
            # Min value combination
            min_combo = base_params.copy()
            min_combo[param_name] = min_val
            edge_combinations.append(min_combo)
            
            # Max value combination
            max_combo = base_params.copy()
            max_combo[param_name] = max_val
            edge_combinations.append(max_combo)
        
        # Remove duplicates
        unique_combinations = []
        for combo in edge_combinations:
            if combo not in unique_combinations:
                unique_combinations.append(combo)
        
        return unique_combinations

    def evaluate_parameters(self, params):
        """Evaluate a single parameter combination"""
        try:
            # Add fixed ATR period
            strategy_params = params.copy()
            strategy_params['atr_period'] = 14

            # Run full market backtest
            trades_csv_path = run_backtest_with_params(
                strategy_params,
                start_date=self.in_sample_start,
                end_date=self.in_sample_end
            )

            # Load trades
            trades_df = pd.read_csv(trades_csv_path, dtype={'StockCode': str})
            trades_df = add_hk_stock_code_prefix(trades_df)

            if len(trades_df) == 0:
                return {
                    'params': params,
                    'sharpe': -np.inf,
                    'cagr': 0,
                    'max_drawdown': -1,
                    'num_trades': 0,
                    'error': 'No trades generated'
                }

            # Run portfolio backtest
            portfolio_results = run_portfolio_backtest(
                trades_df=trades_df.copy(),
                market_data_df=self.market_data_df,
                benchmark_data_df=self.benchmark_data_df,
                max_position_percent=0.05,
                start_date=self.in_sample_start,
                end_date=self.in_sample_end,
                title_suffix="optimization",
                show_plot=False
            )

            result = {
                'params': params,
                'sharpe': portfolio_results.get('sharpe', -np.inf),
                'cagr': portfolio_results.get('cagr', 0),
                'max_drawdown': portfolio_results.get('max_drawdown', -1),
                'num_trades': len(trades_df),
                'error': None
            }

            # Update best parameters
            if result['sharpe'] > self.best_sharpe:
                self.best_sharpe = result['sharpe']
                self.best_params = params.copy()
                logger.info(f"🎯 NEW BEST Sharpe: {self.best_sharpe:.4f} | Params: {params}")

            return result

        except Exception as e:
            logger.error(f"❌ Error evaluating {params}: {str(e)}")
            return {
                'params': params,
                'sharpe': -np.inf,
                'cagr': 0,
                'max_drawdown': -1,
                'num_trades': 0,
                'error': str(e)
            }

    def optimize(self):
        """Run the optimization"""
        logger.info("🚀 Starting 7-hour parameter optimization for Hong Kong...")

        start_time = datetime.now()
        combinations = self.get_smart_parameter_combinations()

        logger.info(f"⏱️  Estimated completion time: {len(combinations) * 2:.0f} minutes")

        # Test each combination
        for i, params in enumerate(tqdm(combinations, desc="Optimizing parameters")):
            result = self.evaluate_parameters(params)
            self.results.append(result)

            # Progress logging
            if (i + 1) % max(1, len(combinations) // 10) == 0:
                elapsed = (datetime.now() - start_time).total_seconds() / 3600
                remaining = (len(combinations) - i - 1) * (elapsed / (i + 1))
                logger.info(f"📊 Progress: {i+1}/{len(combinations)} ({(i+1)/len(combinations)*100:.1f}%)")
                logger.info(f"⏰ Elapsed: {elapsed:.1f}h, Remaining: {remaining:.1f}h")
                logger.info(f"🏆 Current best Sharpe: {self.best_sharpe:.4f}")

        total_time = (datetime.now() - start_time).total_seconds() / 3600
        logger.info(f"✅ Optimization completed in {total_time:.1f} hours!")

        return self.analyze_results()

    def analyze_results(self):
        """Analyze and save results"""
        if not self.results:
            logger.error("No results to analyze")
            return None

        # Filter valid results
        valid_results = [r for r in self.results if r['error'] is None and r['sharpe'] > -np.inf]

        if len(valid_results) == 0:
            logger.error("No valid results found")
            return None

        # Sort by Sharpe ratio
        valid_results.sort(key=lambda x: x['sharpe'], reverse=True)
        top_10 = valid_results[:10]

        logger.info("\n" + "="*80)
        logger.info("🏆 HONG KONG OPTIMIZATION RESULTS")
        logger.info("="*80)
        logger.info(f"Total combinations tested: {len(self.results)}")
        logger.info(f"Valid results: {len(valid_results)}")
        logger.info(f"Best Sharpe ratio: {self.best_sharpe:.4f}")
        logger.info(f"Best parameters: {self.best_params}")

        logger.info("\n🥇 Top 10 parameter combinations:")
        for i, result in enumerate(top_10, 1):
            logger.info(f"{i:2d}. Sharpe: {result['sharpe']:.4f}, CAGR: {result['cagr']:.2%}, "
                       f"MaxDD: {result['max_drawdown']:.2%}, Trades: {result['num_trades']}")
            logger.info(f"    {result['params']}")

        # Save results
        self.save_results(valid_results)

        return {
            'best_params': self.best_params,
            'best_sharpe': self.best_sharpe,
            'top_10_results': top_10,
            'all_results': valid_results
        }

    def save_results(self, results):
        """Save optimization results"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save detailed results to CSV
        results_df = pd.DataFrame(results)
        results_path = os.path.join(
            self.project_root, 'RoC', 'backtest_results',
            f'quick_7h_optimization_hk_{timestamp}.csv'
        )
        results_df.to_csv(results_path, index=False)
        logger.info(f"📁 Results saved to: {results_path}")

        # Save best parameters to JSON
        best_params_path = os.path.join(
            self.project_root, 'RoC', 'backtest_results',
            f'best_parameters_7h_hk_{timestamp}.json'
        )

        summary = {
            'optimization_timestamp': timestamp,
            'optimization_mode': '7-hour quick optimization - Hong Kong',
            'target_hours': self.target_hours,
            'combinations_tested': len(self.results),
            'best_sharpe_ratio': float(self.best_sharpe),
            'best_parameters': self.best_params,
            'in_sample_period': f"{self.in_sample_start} to {self.in_sample_end}",
            'market': 'Hong Kong'
        }

        with open(best_params_path, 'w') as f:
            json.dump(summary, f, indent=2)
        logger.info(f"🎯 Best parameters saved to: {best_params_path}")

def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(description='Quick 7-hour RoC parameter optimization - Hong Kong')
    parser.add_argument('--hours', type=float, default=7.0, help='Target optimization time in hours')

    args = parser.parse_args()

    logger.info("="*80)
    logger.info("🚀 QUICK 7-HOUR ROC PARAMETER OPTIMIZATION - HONG KONG")
    logger.info("="*80)
    logger.info(f"Target time: {args.hours} hours")
    logger.info(f"Objective: Maximize Sharpe ratio on in-sample data (2010-2023)")
    logger.info(f"Market: Hong Kong")

    optimizer = Quick7HourOptimizerHK(target_hours=args.hours)
    results = optimizer.optimize()

    if results:
        logger.info("\n" + "="*80)
        logger.info("🎉 HONG KONG OPTIMIZATION COMPLETED SUCCESSFULLY!")
        logger.info("="*80)
        logger.info("Next steps:")
        logger.info("1. Review the results above")
        logger.info("2. Update the parameters in run_full_market_backtest_RoC_HK.py")
        logger.info("3. Test the optimized parameters on Hong Kong data")
    else:
        logger.error("❌ Optimization failed!")

if __name__ == "__main__":
    main()
