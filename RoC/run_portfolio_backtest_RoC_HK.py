# strategies/RoC/run_portfolio_backtest_RoC_HK.py - Hong Kong Version
import sys
import os
import pandas as pd
import matplotlib.pyplot as plt
import ffn
import numpy as np
from tqdm import tqdm
import gc  # 添加垃圾回收
import logging
import json

# --- Path Setup & Font ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- Logging Setup ---
def setup_logging(level='INFO'):
    """Setup logging configuration with specified level."""
    log_level = getattr(logging, level.upper(), logging.INFO)
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    return logging.getLogger(__name__)

# Initialize logger (will be reconfigured in main())
logger = logging.getLogger(__name__)

# Import from your new utility module
from utilities.utils_portfolio import run_portfolio_simulation, run_stochastic_portfolio_simulation
from utilities.complete_font_solution import setup_chinese_font
from utilities.utils import load_stock_name_mapping, load_hk_stock_name_mapping

# 导入内存优化工具
try:
    from utilities.performance_optimizations import optimize_dataframe_memory
    MEMORY_OPTIMIZATION_AVAILABLE = True
    logger.info("Memory optimization module loaded for portfolio backtesting")
except ImportError:
    MEMORY_OPTIMIZATION_AVAILABLE = False
    logger.warning("Memory optimization module not available, using standard processing")
    def optimize_dataframe_memory(df, preserve_price_precision=True):
        return df

# ============================================================================
# SMART DYNAMIC FILTERING CONFIGURATION
# ============================================================================
# This section contains all configurable parameters for the smart filtering system.
# Modify these values to adjust filtering behavior without changing the core logic.

class PositionWeightConfig:
    """Configuration for dynamic position weighting based on factor scores."""

    # --- Factor Columns ---
    REQUIRED_FACTORS = ['RoC(5)', 'Vol(5)', 'Max(5)', 'Abn_turnover(5)']

    # --- Position Weighting Quantiles ---
    # Volatility quantiles for position weighting
    VOL_Q7_QUANTILE = 0.7   # Good volatility threshold
    VOL_Q8_QUANTILE = 0.8   # Excellent volatility threshold
    VOL_Q9_QUANTILE = 0.9   # Top-tier volatility threshold

    # RoC (momentum reversal) quantiles for position weighting
    ROC_Q1_QUANTILE = 0.1   # Top-tier reversal threshold (lowest momentum)
    ROC_Q2_QUANTILE = 0.2   # Excellent reversal threshold
    ROC_Q3_QUANTILE = 0.3   # Good reversal threshold

    # Max (momentum continuation) quantiles for position weighting
    MAX_Q9_QUANTILE = 0.9   # Excellent momentum threshold
    MAX_Q10_QUANTILE = 1.0  # Top-tier momentum threshold (highest 10%)

    # Poor performance thresholds for downweighting
    VOL_POOR_QUANTILE = 0.1   # Poor volatility threshold
    ROC_POOR_QUANTILE = 0.9   # Poor momentum threshold (too high)
    MAX_POOR_QUANTILE = 0.1   # Poor Max threshold (too low)

    # --- Position Weight Multipliers ---
    POSITION_WEIGHT_TOP_TIER = 2.5      # Vol≥Q9 AND RoC≤Q1
    POSITION_WEIGHT_EXCELLENT = 2.0     # Vol≥Q8 AND RoC≤Q2
    POSITION_WEIGHT_GOOD = 1.5          # Vol≥Q7 AND RoC≤Q3
    POSITION_WEIGHT_NORMAL = 1.0        # Standard weight
    POSITION_WEIGHT_POOR = 0.5          # Vol≤Q1 OR RoC≥Q9

    # --- Display Configuration ---
    # Weight ranges for display purposes
    WEIGHT_RANGES = {
        'top_tier': (2.3, float('inf'), 'Top Tier (2.5x weight): Vol Q9+ & RoC Q1- & Max Q10'),
        'excellent': (1.8, 2.3, 'Excellent (2.0x weight): Strong multi-factor signals'),
        'good': (1.3, 1.8, 'Good (1.5x weight): Moderate multi-factor signals'),
        'above_normal': (1.1, 1.3, 'Above Normal (1.2x weight): Single strong factor'),
        'normal': (0.8, 1.1, 'Normal (1.0x weight): Baseline signals'),
        'poor': (0, 0.8, 'Poor (0.5x weight): Multiple poor factors')
    }

    @classmethod
    def calculate_position_weight(cls, row, thresholds):
        """Calculate position weight based on factor scores with Max factor enhancement."""
        weight = cls.POSITION_WEIGHT_NORMAL

        # Count positive factors for enhanced weighting
        positive_factors = 0

        # Vol factor (high volatility is positive)
        if row['Vol(5)'] >= thresholds['vol_q9']:
            positive_factors += 2  # Q9+ gets 2 points
        elif row['Vol(5)'] >= thresholds['vol_q8']:
            positive_factors += 1.5  # Q8+ gets 1.5 points
        elif row['Vol(5)'] >= thresholds['vol_q7']:
            positive_factors += 1  # Q7+ gets 1 point

        # RoC factor (low momentum/reversal is positive)
        if row['RoC(5)'] <= thresholds['roc_q1']:
            positive_factors += 2  # Q1- gets 2 points
        elif row['RoC(5)'] <= thresholds['roc_q2']:
            positive_factors += 1.5  # Q2- gets 1.5 points
        elif row['RoC(5)'] <= thresholds['roc_q3']:
            positive_factors += 1  # Q3- gets 1 point

        # Max factor (high momentum continuation is positive) - NEW
        if row['Max(5)'] >= thresholds['max_q10']:
            positive_factors += 1.5  # Q10 gets 1.5 points
        elif row['Max(5)'] >= thresholds['max_q9']:
            positive_factors += 1  # Q9 gets 1 point

        # Calculate weight based on total positive factors
        if positive_factors >= 4.5:  # Top tier (e.g., Vol Q9+ & RoC Q1- & Max Q10)
            weight = cls.POSITION_WEIGHT_TOP_TIER
        elif positive_factors >= 3.5:  # Excellent
            weight = cls.POSITION_WEIGHT_EXCELLENT
        elif positive_factors >= 2.0:  # Good
            weight = cls.POSITION_WEIGHT_GOOD
        elif positive_factors >= 1.0:  # Above normal
            weight = 1.2

        # Downweight poor signals
        poor_factors = 0
        if row['Vol(5)'] <= thresholds['vol_poor']:
            poor_factors += 1
        if row['RoC(5)'] >= thresholds['roc_poor']:
            poor_factors += 1
        if row['Max(5)'] <= thresholds['max_poor']:
            poor_factors += 1

        if poor_factors >= 2:  # Multiple poor factors
            weight = cls.POSITION_WEIGHT_POOR

        return weight

# --- Output Formatting Utilities ---
def print_section_header(title, level=1):
    """Print a formatted section header."""
    if level == 1:
        separator = "=" * 60
        logger.info(f"\n{separator}")
        logger.info(f"  {title}")
        logger.info(separator)
    elif level == 2:
        separator = "-" * 40
        logger.info(f"\n{separator}")
        logger.info(f"  {title}")
        logger.info(separator)

def print_filter_summary(filter_stats):
    """Print a consolidated filter summary."""
    summary = (
        f"Filter Summary:\n"
        f"  Type: {filter_stats['filter_type']}\n"
        f"  Original Trades: {filter_stats['original_trades']:,}\n"
        f"  Filtered Trades: {filter_stats['filtered_trades']:,}\n"
        f"  Retention Rate: {filter_stats['retention_rate']:.1f}%\n"
        f"  Expected Effect: {filter_stats['expected_effect']}"
    )
    logger.info(summary)

def print_weight_distribution(trades_df):
    """Print position weight distribution summary."""
    logger.info("Position Weight Distribution:")
    weight_ranges = PositionWeightConfig.WEIGHT_RANGES
    for range_name, (min_weight, max_weight, description) in weight_ranges.items():
        if max_weight == float('inf'):
            count = len(trades_df[trades_df['PositionWeight'] >= min_weight])
        else:
            count = len(trades_df[
                (trades_df['PositionWeight'] >= min_weight) &
                (trades_df['PositionWeight'] < max_weight)
            ])
        logger.info(f"  {description}: {count:,}")

    avg_weight = trades_df['PositionWeight'].mean()
    weight_range = f"{trades_df['PositionWeight'].min():.2f}x - {trades_df['PositionWeight'].max():.2f}x"
    logger.info(f"  Average Weight: {avg_weight:.2f}x")
    logger.info(f"  Weight Range: {weight_range}")

def print_backtest_summary(stats_dict, trade_stats=None):
    """Print consolidated backtest results summary."""
    logger.info("Performance Summary:")
    logger.info(f"  CAGR: {stats_dict.get('cagr', 0):.2%}")
    logger.info(f"  Sharpe Ratio: {stats_dict.get('sharpe', 0):.2f}")
    logger.info(f"  Max Drawdown: {stats_dict.get('max_drawdown', 0):.2%}")

    if trade_stats:
        logger.info("Trade Statistics:")
        for metric, value in trade_stats.items():
            logger.info(f"  {metric}: {value}")

def print_data_loading_summary(data_info):
    """Print data loading summary."""
    logger.info("Data Loading Summary:")
    for data_type, info in data_info.items():
        logger.info(f"  {data_type}: {info}")

def print_memory_usage():
    """Print current memory usage if available."""
    try:
        import psutil
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        logger.debug(f"Current memory usage: {memory_mb:.1f} MB")
    except ImportError:
        logger.debug("Memory usage monitoring not available (psutil not installed)")

def log_progress(message, level='info'):
    """Log progress messages with appropriate level."""
    if level.lower() == 'debug':
        logger.debug(message)
    elif level.lower() == 'warning':
        logger.warning(message)
    else:
        logger.info(message)

# --- User Input Utilities ---
def get_user_input(prompt, default, valid_options=None, input_type=str):
    """
    Simplified user input handler with defaults and validation.

    Args:
        prompt (str): The prompt message to display
        default: Default value if user presses enter
        valid_options (list): List of valid options (case-insensitive)
        input_type (type): Type to convert input to (str, int, float)

    Returns:
        Converted and validated user input or default value
    """
    try:
        response = input(f"{prompt} (default: {default}): ").strip()

        # Use default if empty response
        if not response:
            return default

        # Convert to appropriate type
        if input_type != str:
            response = input_type(response)
        else:
            response = response.lower()

        # Validate against options if provided
        if valid_options and response not in valid_options:
            raise ValueError(f"Invalid input. Choose from {valid_options}")

        return response

    except KeyboardInterrupt:
        logger.info("User cancelled input. Using default.")
        return default
    except ValueError as e:
        logger.warning(f"Invalid input: {e}. Using default.")
        return default

def add_hk_stock_code_prefix(df):
    """Adds 'HK.' prefix to Hong Kong stock codes if not already present."""

    def get_hk_prefix(code):
        if isinstance(code, str):
            # Check if prefix already exists
            if code.startswith('HK.'):
                return code
            # Ensure 5-digit format for HK stocks
            code = code.zfill(5)
            return f"HK.{code}"
        return code # Return original if not a string

    # Ensure StockCode is string type before applying string operations
    if 'StockCode' in df.columns:
        df['StockCode'] = df['StockCode'].astype(str)
        df['StockCode'] = df['StockCode'].apply(get_hk_prefix)
    return df

def create_position_sizer(max_position_percent):
    """
    Creates a position sizing function that limits the position size
    to a percentage of the total Net Asset Value (NAV).

    Args:
        max_position_percent (float): The maximum percentage (e.g., 0.10 for 10%).

    Returns:
        function: A function compatible with the portfolio simulation.
    """
    if not 0 < max_position_percent <= 1:
        raise ValueError("max_position_percent must be between 0 and 1.")

    def position_sizer(total_nav, price_per_share, stock_code=None, entry_date=None):
        return total_nav * max_position_percent

    return position_sizer

def create_smart_position_sizer(base_position_percent, trades_df):
    """
    创建智能仓位管理器，根据因子评分动态调整仓位大小

    Args:
        base_position_percent (float): 基础仓位百分比 (e.g., 0.05 for 5%)
        trades_df (DataFrame): 包含PositionWeight列的交易数据

    Returns:
        function: 智能仓位管理函数
    """
    if not 0 < base_position_percent <= 1:
        raise ValueError("base_position_percent must be between 0 and 1.")

    # 创建股票代码到仓位权重的映射
    if 'PositionWeight' in trades_df.columns and 'StockCode' in trades_df.columns:
        # 为每个股票代码和日期组合创建权重映射
        weight_mapping = {}
        for _, row in trades_df.iterrows():
            key = (row['StockCode'], pd.to_datetime(row['EntryDate']).strftime('%Y-%m-%d'))
            weight_mapping[key] = row.get('PositionWeight', 1.0)
    else:
        weight_mapping = {}

    def smart_position_sizer(total_nav, price_per_share, stock_code=None, entry_date=None):
        # 获取该股票的仓位权重
        if stock_code and entry_date:
            date_str = pd.to_datetime(entry_date).strftime('%Y-%m-%d')
            key = (stock_code, date_str)
            weight = weight_mapping.get(key, 1.0)
        else:
            weight = 1.0

        # 计算调整后的仓位大小
        adjusted_position_percent = base_position_percent * weight

        # 确保不超过最大限制 (10%)
        adjusted_position_percent = min(adjusted_position_percent, 0.10)

        return total_nav * adjusted_position_percent

    return smart_position_sizer

def ask_user_for_monte_carlo():
    """
    Simplified Monte Carlo simulation prompt.
    Returns: (run_simulation, n_simulations)
    """
    print_section_header("🎯 Backtest Completed!", level=1)

    # Ask if user wants to run Monte Carlo
    run_mc = get_user_input("Run Monte Carlo simulation? (y/n)", "n", ["y", "n"]) == "y"
    if not run_mc:
        logger.info("Skipping Monte Carlo simulation")
        return False, 0

    # Get number of simulations
    n_simulations = get_user_input("Number of simulations", 30, input_type=int)
    if n_simulations <= 0:
        logger.warning("Invalid simulation count. Using default.")
        n_simulations = 30

    # Show estimated time and confirm
    estimated_time = n_simulations * 8
    minutes, seconds = estimated_time // 60, estimated_time % 60
    logger.info(f"Will run {n_simulations} simulations, estimated time: {minutes}m {seconds}s")

    confirm = get_user_input("Confirm start? (y/n)", "y", ["y", "n"]) == "y"
    return (confirm, n_simulations) if confirm else (False, 0)

# ST, delisted, and BSE stock filtering is now handled in the pre-processing stage

def run_advanced_monte_carlo(
    n_simulations,
    trades_df,
    market_data_df,
    start_date,
    end_date,
    initial_capital,
    commission_rate,
    stamp_duty_rate,
    max_position_percent,
    show_plot=False
):
    """
    Runs a Monte Carlo simulation using the stochastic, chronological method
    to account for signal selection luck.
    """
    print_section_header(f"Advanced Monte Carlo Simulation ({n_simulations} iterations)", level=1)

    results = []

    # Create the appropriate position sizer based on available data
    if 'PositionWeight' in trades_df.columns:
        logger.debug("Using smart position sizer for Monte Carlo")
        position_sizer_fn = create_smart_position_sizer(max_position_percent, trades_df)
    else:
        logger.debug("Using standard position sizer for Monte Carlo")
        position_sizer_fn = create_position_sizer(max_position_percent)

    # Filter trades for the simulation period just once
    trades_df['EntryDate'] = pd.to_datetime(trades_df['EntryDate'])
    sim_trades_df = trades_df[
        (trades_df['EntryDate'] >= pd.to_datetime(start_date)) &
        (trades_df['EntryDate'] <= pd.to_datetime(end_date))
    ].copy()

    if sim_trades_df.empty:
        logger.warning("Trade log is empty for the simulation period. Skipping Advanced Monte Carlo.")
        return None

    # Use tqdm for a progress bar over the simulations
    for i in tqdm(range(n_simulations), desc="Advanced Monte Carlo"):
        # Run the stochastic simulation. Each run will be different due to random shuffling on cluster days.
        nav_history, _ = run_stochastic_portfolio_simulation(
            trades_df=sim_trades_df, # Pass the pre-filtered trades for the period
            market_data_df=market_data_df.copy(),
            start_date=start_date,
            initial_capital=initial_capital,
            commission_rate=commission_rate,
            stamp_duty_rate=stamp_duty_rate,
            position_sizing_fn=position_sizer_fn
        )

        if not nav_history.empty and nav_history['NAV'].nunique() > 1:
            stats = ffn.PerformanceStats(nav_history['NAV'])
            results.append({
                'cagr': stats.cagr,
                'sharpe': stats.daily_sharpe,
                'max_drawdown': stats.max_drawdown,
                'annual_vol': stats.yearly_vol
            })

    if not results:
        logger.warning("Advanced Monte Carlo simulation produced no valid results.")
        return None

    # --- Analyze and Display Monte Carlo Results ---
    results_df = pd.DataFrame(results)

    print_section_header("Monte Carlo Results Summary", level=2)
    logger.info("Statistical Distribution:")
    logger.info(f"\n{results_df.describe(percentiles=[.05, .25, .5, .75, .95])}")

    if show_plot:
        plot_dir = os.path.join(os.path.dirname(__file__), 'backtest_results')
        os.makedirs(plot_dir, exist_ok=True)

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Advanced Monte Carlo: Distribution of Performance Metrics', fontsize=18)

        results_df['cagr'].plot(kind='hist', ax=axes[0, 0], bins=50, alpha=0.7, title='CAGR Distribution')
        axes[0, 0].set_xlabel('CAGR')
        axes[0, 0].set_ylabel('Frequency')

        results_df['sharpe'].plot(kind='hist', ax=axes[0, 1], bins=50, alpha=0.7, title='Sharpe Ratio Distribution')
        axes[0, 1].set_xlabel('Daily Sharpe Ratio')

        results_df['max_drawdown'].plot(kind='hist', ax=axes[1, 0], bins=50, alpha=0.7, title='Max Drawdown Distribution')
        axes[1, 0].set_xlabel('Max Drawdown')
        axes[1, 0].set_ylabel('Frequency')

        results_df['annual_vol'].plot(kind='hist', ax=axes[1, 1], bins=50, alpha=0.7, title='Annual Volatility Distribution')
        axes[1, 1].set_xlabel('Annual Volatility')

        plt.tight_layout(rect=[0, 0.03, 1, 0.96])

        plot_path = os.path.join(plot_dir, 'advanced_monte_carlo_distribution.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        logger.info(f"Monte Carlo distribution plot saved to: {plot_path}")
        plt.show()

    summary_stats = {
        'mc_median_cagr': results_df['cagr'].median(),
        'mc_median_sharpe': results_df['sharpe'].median(),
        'mc_median_max_drawdown': results_df['max_drawdown'].median(),
        'mc_cagr_5th_percentile': results_df['cagr'].quantile(0.05),
        'mc_drawdown_95th_percentile': results_df['max_drawdown'].quantile(0.95),
        'mc_sharpe_5th_percentile': results_df['sharpe'].quantile(0.05)
    }
    return summary_stats

def run_portfolio_backtest(trades_df, market_data_df, benchmark_data_df, max_position_percent, start_date, end_date, title_suffix="", show_plot=False):
    # Call font setup for ffn plots
    setup_chinese_font()

    # --- Constants ---
    INITIAL_CAPITAL = 1_000_000
    COMMISSION_RATE = 0.0003
    STAMP_DUTY_RATE = 0.0005

    # --- Filter trades for the specified period ---
    trades_df['EntryDate'] = pd.to_datetime(trades_df['EntryDate'])
    trades_df = trades_df[(trades_df['EntryDate'] >= start_date) & (trades_df['EntryDate'] <= end_date)]

    if trades_df.empty:
        logger.warning(f"Trade log is empty for the period {start_date} to {end_date}. Skipping portfolio backtest.")
        return {'cagr': -1, 'sharpe': 0, 'max_drawdown': -1}

    # Create the smart position sizer if PositionWeight column exists
    if 'PositionWeight' in trades_df.columns:
        logger.info("Using smart dynamic position sizing")
        position_sizer_fn = create_smart_position_sizer(max_position_percent, trades_df)
    else:
        logger.info("Using standard position sizing")
        position_sizer_fn = create_position_sizer(max_position_percent)

    # NOTE: Using the original, deterministic simulation for the main FFN plot
    nav_history, completed_trades = run_portfolio_simulation(
        trades_df=trades_df,
        market_data_df=market_data_df.copy(),
        start_date=start_date,
        initial_capital=INITIAL_CAPITAL,
        commission_rate=COMMISSION_RATE,
        stamp_duty_rate=STAMP_DUTY_RATE,
        position_sizing_fn=position_sizer_fn
    )

    # --- Save Completed Trades ---
    if not completed_trades.empty:
        # --- Load Factor Data from Full Market Backtest ---
        completed_trades['EntryDate'] = pd.to_datetime(completed_trades['EntryDate'])

        # Load factor data from full market backtest results (Hong Kong version)
        full_market_trades_path = os.path.join(project_root, 'RoC', 'backtest_results', 'hk_full_market_trades_RoC.csv')

        if os.path.exists(full_market_trades_path):
            logger.debug("Loading factor data from Hong Kong full market backtest results...")

            # Load full market trades with factors
            full_market_df = pd.read_csv(full_market_trades_path)
            full_market_df['EntryDate'] = pd.to_datetime(full_market_df['EntryDate'])

            # Select only the factor columns we need
            factor_columns = ['StockCode', 'EntryDate', 'RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']
            available_factor_columns = [col for col in factor_columns if col in full_market_df.columns]

            if len(available_factor_columns) > 2:  # At least StockCode, EntryDate, and one factor
                factor_data = full_market_df[available_factor_columns].copy()

                # Merge factor data with completed trades
                completed_trades = pd.merge(
                    completed_trades,
                    factor_data,
                    on=['StockCode', 'EntryDate'],
                    how='left'
                )
                logger.debug(f"Successfully merged factor data for {len(completed_trades)} completed trades")
            else:
                logger.warning("Factor columns not found in Hong Kong full market trades file")
        else:
            logger.warning("Hong Kong full market trades file not found")

        # Note: Volatility and EMA20 indicators have been removed
        # Only factor data (RoC, Max, Vol, Abn_turnover) is loaded from full market backtest results

        completed_trades['PnL %'] = (completed_trades['PnL'] / (completed_trades['EntryPrice'] * completed_trades['Shares'])) * 100

        output_dir = os.path.join(os.path.dirname(__file__), 'backtest_results')
        os.makedirs(output_dir, exist_ok=True)
        trades_output_path = os.path.join(output_dir, f'completed_trades_hk{title_suffix and f"_{title_suffix}"}.csv')

        # --- Add Stock Names ---
        logger.debug("Loading Hong Kong stock names for trade log...")
        stock_name_map = load_hk_stock_name_mapping(project_root)
        completed_trades['StockName'] = completed_trades['StockCode'].map(stock_name_map)

        # --- Add Exit NAV to Completed Trades ---
        if not completed_trades.empty:
            nav_history_reset = nav_history.reset_index()  # Ensure 'Date' is a column
            nav_history_reset['Date'] = pd.to_datetime(nav_history_reset['Date'])
            completed_trades['ExitDate'] = pd.to_datetime(completed_trades['ExitDate'])
            # For each trade, find the NAV at the exit date (or last available NAV before exit date)
            exit_navs = []
            nav_history_sorted = nav_history_reset.sort_values('Date')
            for exit_date in completed_trades['ExitDate']:
                # Find the last NAV on or before the exit date
                nav_row = nav_history_sorted[nav_history_sorted['Date'] <= exit_date].tail(1)
                if not nav_row.empty:
                    exit_navs.append(nav_row.iloc[0]['NAV'])
                else:
                    exit_navs.append(float('nan'))
            completed_trades['ExitNAV'] = exit_navs

        # --- Reorder columns for better readability ---
        desired_cols = [
            'StockCode', 'StockName', 'EntryDate', 'EntryPrice', 'Shares',
            'ExitDate', 'ExitPrice', 'ExitReason', 'PnL', 'PnL %',
            'RoC(5)', 'Max(5)', 'Abn_turnover(5)', 'Vol(5)', 'ExitNAV'
        ]
        existing_cols = [col for col in desired_cols if col in completed_trades.columns]
        completed_trades = completed_trades[existing_cols]

        # Ensure date columns are formatted correctly
        for col in ['EntryDate', 'ExitDate']:
            if col in completed_trades.columns:
                completed_trades[col] = pd.to_datetime(completed_trades[col]).dt.strftime('%Y-%m-%d')
        completed_trades.to_csv(trades_output_path, index=False, encoding='utf-8-sig', float_format='%.4f')
        logger.info(f"Completed trades log saved to: {trades_output_path}")

    # --- Save NAV History ---
    if not nav_history.empty:
        output_dir = os.path.join(os.path.dirname(__file__), 'backtest_results')
        nav_history_output_path = os.path.join(output_dir, f'nav_history_hk{title_suffix and f"_{title_suffix}"}.csv')
        nav_history.to_csv(nav_history_output_path)
        logger.info(f"NAV history saved to: {nav_history_output_path}")

    # --- FFN Performance Analysis ---
    print_section_header(f"Portfolio Backtest Results - {title_suffix.replace('_', ' ').title()}", level=1)

    if nav_history.empty or nav_history['NAV'].nunique() < 2:
        logger.warning("Not enough data for FFN analysis.")
        return {'cagr': -1, 'sharpe': 0, 'max_drawdown': -1}

    # 1. Prepare benchmark series, rebased to initial capital
    benchmark_data_df = benchmark_data_df[benchmark_data_df.index >= nav_history.index[0]]
    benchmark_nav = (benchmark_data_df['Close'] / benchmark_data_df['Close'].iloc[0]) * INITIAL_CAPITAL
    benchmark_nav.name = '恒生指数基准'  # Changed to Hang Seng Index for Hong Kong

    # 2. Rename strategy NAV series
    strategy_nav = nav_history['NAV'].rename('港股策略')  # Changed to Hong Kong Strategy

    # Ensure both series end at the specified end_date
    strategy_nav = strategy_nav[strategy_nav.index <= end_date]
    benchmark_nav = benchmark_nav[benchmark_nav.index <= end_date]

    # 3. Combine and run ffn analysis
    nav_data = ffn.merge(strategy_nav, benchmark_nav)

    # 4. Display performance stats in the console
    stats = nav_data.calc_stats()
    stats.display()

    # --- Custom Trade-Based Metrics ---
    if not completed_trades.empty:
        num_trades = len(completed_trades)
        winning_trades = completed_trades[completed_trades['PnL'] > 0]
        losing_trades = completed_trades[completed_trades['PnL'] < 0]

        win_rate = len(winning_trades) / num_trades if num_trades > 0 else 0

        gross_profit = winning_trades['PnL'].sum()
        gross_loss = abs(losing_trades['PnL'].sum())
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else np.inf

        # Calculate PnL % based on initial investment per trade
        initial_investment = completed_trades['EntryPrice'] * completed_trades['Shares']
        # Avoid division by zero for trades with no cost
        valid_investment_mask = initial_investment != 0
        avg_pnl_percent = (completed_trades.loc[valid_investment_mask, 'PnL'] / initial_investment[valid_investment_mask]).mean() * 100

        # Ensure dates are datetime objects for calculation
        completed_trades['EntryDate'] = pd.to_datetime(completed_trades['EntryDate'])
        completed_trades['ExitDate'] = pd.to_datetime(completed_trades['ExitDate'])
        avg_holding_days = (completed_trades['ExitDate'] - completed_trades['EntryDate']).dt.days.mean()

        # Create a summary DataFrame
        trade_stats_data = {
            'Metric': [
                'Total Trades',
                'Win Rate',
                'Profit Factor',
                'Avg. PnL % per Trade',
                'Avg. Holding Days'
            ],
            'Value': [
                f'{num_trades}',
                f'{win_rate:.2%}',
                f'{profit_factor:.2f}',
                f'{avg_pnl_percent:.2f}%' if pd.notna(avg_pnl_percent) else 'N/A',
                f'{avg_holding_days:.2f}' if pd.notna(avg_holding_days) else 'N/A'
            ]
        }
        trade_stats_summary = pd.DataFrame(trade_stats_data).set_index('Metric')

        print_section_header("Additional Trade-Based Metrics", level=2)
        logger.info(f"\n{trade_stats_summary}")

    if show_plot:
        # 5. Plot the performance summary and save it
        plot_dir = os.path.join(os.path.dirname(__file__), 'backtest_results')
        os.makedirs(plot_dir, exist_ok=True)
        plot_path = os.path.join(plot_dir, f'ffn_portfolio_performance_hk{title_suffix and f"_{title_suffix}"}.png')

        ax = nav_data.plot(
            title=f"港股策略 vs. 恒生指数基准 ({title_suffix.replace('_', ' ').title()})",
            figsize=(15, 10),
            logy=True,
        )

        nav_history['PositionRatio'] = nav_history['PositionsValue'] / nav_history['NAV']
        ax2 = ax.twinx()
        ax2.fill_between(
            nav_history.index,
            nav_history['PositionRatio'],
            alpha=0.2,
            color='grey',
            label='仓位'
        )
        ax2.set_ylabel('仓位比例 (Position Ratio)')
        ax2.set_ylim(0, 1.05)
        ax2.grid(False)

        lines, labels = ax.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax.legend(lines + lines2, labels + labels2, loc='upper left')

        ax.figure.savefig(plot_path, dpi=300, bbox_inches='tight')
        logger.info(f"Performance chart saved to: {plot_path}")
        plt.show()

    strategy_stats_series = stats.get('港股策略')  # Changed to match the new name
    if strategy_stats_series is not None:
        return {
            'cagr': strategy_stats_series.cagr,
            'sharpe': strategy_stats_series.daily_sharpe,
            'max_drawdown': strategy_stats_series.max_drawdown
        }
    else:
        logger.warning("Strategy stats not found. Returning empty metrics.")
        return {'cagr': -1, 'sharpe': 0, 'max_drawdown': -1}

def main():
    """
    Main function to run the full Hong Kong portfolio backtest and analysis.
    """
    # Setup logging based on environment variable or default to INFO
    log_level = os.getenv('LOG_LEVEL', 'INFO')
    global logger
    logger = setup_logging(log_level)

    print_section_header("Hong Kong RoC Portfolio Backtest System", level=1)

    logger.info("Loading pre-generated Hong Kong trade signals...")
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    trades_df_path = os.path.join(project_root, 'RoC', 'backtest_results', 'hk_full_market_trades_RoC.csv')

    try:
        trades_df = pd.read_csv(trades_df_path, dtype={'StockCode': str})
        logger.info(f"Loaded {len(trades_df):,} Hong Kong trade signals")
    except FileNotFoundError:
        logger.error(f"Hong Kong trade signals file not found at {trades_df_path}")
        logger.error("Please run 'run_full_market_backtest_RoC_HK.py' first to generate the trades.")
        return

    trades_df = add_hk_stock_code_prefix(trades_df)
    logger.debug("Added Hong Kong stock code prefixes")

    logger.info("Loading Hong Kong market OHLC data...")
    market_data_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')

    # Load Hong Kong market data
    market_data_df = pd.read_parquet(market_data_path)

    if MEMORY_OPTIMIZATION_AVAILABLE:
        logger.info("Applying memory optimization to Hong Kong market data...")
        market_data_df = optimize_dataframe_memory(market_data_df, preserve_price_precision=True)

    # Rename columns to match expected format
    market_data_df = market_data_df.rename(columns={
        'time_key': 'Date', 'stock_code': 'StockCode', 'close': 'Close',
        'open': 'Open', 'high': 'High', 'low': 'Low', 'volume': 'Volume'
    })
    market_data_df = add_hk_stock_code_prefix(market_data_df)

    logger.info("Loading benchmark data (Hang Seng Index)...")
    # For Hong Kong, we'll use a placeholder benchmark - you may need to adjust this path
    # based on your actual Hang Seng Index data location
    benchmark_path = os.path.join(project_root, 'data', 'hsi_daily.csv')

    # If HSI data doesn't exist, create a simple benchmark from the market data
    if not os.path.exists(benchmark_path):
        logger.warning("Hang Seng Index data not found, creating simple market benchmark...")
        # Create a simple equal-weighted benchmark from available stocks
        market_data_df['Date'] = pd.to_datetime(market_data_df['Date'])
        benchmark_data_df = market_data_df.groupby('Date')['Close'].mean().to_frame()
        benchmark_data_df.columns = ['Close']
        benchmark_data_df.index.name = 'Date'
    else:
        benchmark_data_df = pd.read_csv(benchmark_path, index_col='Date', parse_dates=True)
        benchmark_data_df.rename(columns={'Close': 'Close'}, inplace=True)

    data_info = {
        'Trade Signals': f"{len(trades_df):,} Hong Kong signals loaded",
        'Market Data': f"{len(market_data_df):,} Hong Kong price records",
        'Benchmark': f"Hong Kong benchmark data loaded",
        'Memory Optimization': "Enabled" if MEMORY_OPTIMIZATION_AVAILABLE else "Disabled"
    }
    print_data_loading_summary(data_info)

    initial_trade_count = len(trades_df)
    logger.debug(f"Initial Hong Kong trade count: {initial_trade_count:,}")

    gc.collect()
    print_memory_usage()

    trades_df['EntryDate'] = pd.to_datetime(trades_df['EntryDate'])

    full_market_trades_path = os.path.join(project_root, 'RoC', 'backtest_results', 'hk_full_market_trades_RoC.csv')

    if os.path.exists(full_market_trades_path):
        logger.info("Loading factor data for Hong Kong portfolio filtering...")

        full_market_df = pd.read_csv(full_market_trades_path, encoding='utf-8-sig')
        full_market_df['EntryDate'] = pd.to_datetime(full_market_df['EntryDate'])

        factor_columns = ['StockCode', 'EntryDate', 'RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']
        available_factor_columns = [col for col in factor_columns if col in full_market_df.columns]
        logger.debug(f"Available factor columns: {available_factor_columns}")

        if len(available_factor_columns) > 2:
            factor_data = full_market_df[available_factor_columns].copy()

            # Remove existing factor columns to avoid conflicts
            factor_cols_to_remove = ['RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']
            for col in factor_cols_to_remove:
                if col in trades_df.columns:
                    trades_df = trades_df.drop(columns=[col])
                    logger.debug(f"Removed existing column {col} from trades_df")

            trades_df = pd.merge(trades_df, factor_data, on=['StockCode', 'EntryDate'], how='left')

            factor_cols_check = ['RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']
            existing_factors = [col for col in factor_cols_check if col in trades_df.columns]

            if existing_factors:
                non_null_count = trades_df[existing_factors].notna().all(axis=1).sum()
                logger.info(f"Factor data merged: {non_null_count}/{len(trades_df)} trades have complete data")
            else:
                logger.warning("No factor columns found after merge")
        else:
            logger.warning("Factor columns not found in Hong Kong full market trades file")
    else:
        logger.warning("Hong Kong full market trades file not found")

    logger.info(f"Ready for Hong Kong backtesting with {len(trades_df):,} trades")

    print_section_header("Dynamic Position Weighting", level=1)
    logger.info("Using dynamic position weighting based on factor scores")

    trades_before_factor_filter = len(trades_df)
    logger.info(f"Using all {trades_before_factor_filter:,} Hong Kong trading signals with dynamic position weighting")

    missing_factors = [factor for factor in PositionWeightConfig.REQUIRED_FACTORS if factor not in trades_df.columns]

    if missing_factors:
        logger.warning(f"Missing factor columns {missing_factors}, using standard position sizing")
        factor_filtered_trades = trades_df.copy()
        factor_filtered_trades['PositionWeight'] = 1.0
    else:
        trades_df_clean = trades_df.dropna(subset=PositionWeightConfig.REQUIRED_FACTORS)
        logger.info(f"Cleaned data: {len(trades_df)} -> {len(trades_df_clean)} trades")

        if len(trades_df_clean) == 0:
            logger.error("All trades have missing factor data")
            factor_filtered_trades = trades_df.copy()
            factor_filtered_trades['PositionWeight'] = 1.0
        else:
            factor_filtered_trades = trades_df_clean.copy()

            # Calculate quantile thresholds using configuration
            thresholds = {
                'vol_q7': trades_df_clean['Vol(5)'].quantile(PositionWeightConfig.VOL_Q7_QUANTILE),
                'vol_q8': trades_df_clean['Vol(5)'].quantile(PositionWeightConfig.VOL_Q8_QUANTILE),
                'vol_q9': trades_df_clean['Vol(5)'].quantile(PositionWeightConfig.VOL_Q9_QUANTILE),
                'roc_q1': trades_df_clean['RoC(5)'].quantile(PositionWeightConfig.ROC_Q1_QUANTILE),
                'roc_q2': trades_df_clean['RoC(5)'].quantile(PositionWeightConfig.ROC_Q2_QUANTILE),
                'roc_q3': trades_df_clean['RoC(5)'].quantile(PositionWeightConfig.ROC_Q3_QUANTILE),
                'max_q9': trades_df_clean['Max(5)'].quantile(PositionWeightConfig.MAX_Q9_QUANTILE),
                'max_q10': trades_df_clean['Max(5)'].quantile(PositionWeightConfig.MAX_Q10_QUANTILE),
                'vol_poor': trades_df_clean['Vol(5)'].quantile(PositionWeightConfig.VOL_POOR_QUANTILE),
                'roc_poor': trades_df_clean['RoC(5)'].quantile(PositionWeightConfig.ROC_POOR_QUANTILE),
                'max_poor': trades_df_clean['Max(5)'].quantile(PositionWeightConfig.MAX_POOR_QUANTILE)
            }

            logger.debug("Dynamic position weight thresholds:")
            logger.debug(f"  Vol(5): Q7={thresholds['vol_q7']:.4f}, Q8={thresholds['vol_q8']:.4f}, Q9={thresholds['vol_q9']:.4f}")
            logger.debug(f"  RoC(5): Q1={thresholds['roc_q1']:.4f}%, Q2={thresholds['roc_q2']:.4f}%, Q3={thresholds['roc_q3']:.4f}%")
            logger.debug(f"  Max(5): Q9={thresholds['max_q9']:.4f}, Q10={thresholds['max_q10']:.4f}")

            # Calculate position weights
            factor_filtered_trades['PositionWeight'] = factor_filtered_trades.apply(
                lambda row: PositionWeightConfig.calculate_position_weight(row, thresholds), axis=1
            )

            print_weight_distribution(factor_filtered_trades)

            filter_stats = {
                'filter_type': 'Dynamic Position Weighting',
                'original_trades': trades_before_factor_filter,
                'filtered_trades': len(factor_filtered_trades),
                'retention_rate': 100.0,
                'expected_effect': 'Dynamic position sizing based on factor scores'
            }
            print_filter_summary(filter_stats)

    trades_df = factor_filtered_trades
    logger.info(f"Final trades for Hong Kong backtesting: {len(trades_df):,}")

    if len(trades_df) > 0:
        output_dir = os.path.join(os.path.dirname(__file__), 'backtest_results')
        os.makedirs(output_dir, exist_ok=True)

        filtered_trades_path = os.path.join(output_dir, 'position_weighted_trades_RoC_HK.csv')
        stats_filename = 'position_weight_stats_hk.json'

        trades_df.to_csv(filtered_trades_path, index=False, encoding='utf-8-sig')
        logger.info(f"Hong Kong trade data saved to: {filtered_trades_path}")

        if 'filter_stats' in locals():
            stats_path = os.path.join(output_dir, stats_filename)
            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump(filter_stats, f, indent=2, ensure_ascii=False)
            logger.info(f"Statistics saved to: {stats_path}")

    MAX_POSITION_PERCENT = 0.1
    INITIAL_CAPITAL = 1_000_000
    COMMISSION_RATE = 0.0003
    STAMP_DUTY_RATE = 0.0005
    IN_SAMPLE_START_DATE = '2010-01-01'
    IN_SAMPLE_END_DATE = '2023-12-31'
    OUT_OF_SAMPLE_START_DATE = '2024-01-01'
    OUT_OF_SAMPLE_END_DATE = '2099-12-31'

    filter_name = "Dynamic Position Weighting"
    title_suffix_in = "in_sample_position_weighted"
    title_suffix_out = "out_of_sample_position_weighted"

    print_section_header(f"In-Sample Hong Kong Backtest with {filter_name} ({IN_SAMPLE_START_DATE} to {IN_SAMPLE_END_DATE})", level=1)
    run_portfolio_backtest(
        trades_df=trades_df.copy(),
        market_data_df=market_data_df,
        benchmark_data_df=benchmark_data_df,
        max_position_percent=MAX_POSITION_PERCENT,
        start_date=IN_SAMPLE_START_DATE,
        end_date=IN_SAMPLE_END_DATE,
        title_suffix=title_suffix_in,
        show_plot=True
    )

    print_section_header(f"Out-of-Sample Hong Kong Backtest with {filter_name} ({OUT_OF_SAMPLE_START_DATE} onwards)", level=1)
    run_portfolio_backtest(
        trades_df=trades_df.copy(),
        market_data_df=market_data_df,
        benchmark_data_df=benchmark_data_df,
        max_position_percent=MAX_POSITION_PERCENT,
        start_date=OUT_OF_SAMPLE_START_DATE,
        end_date=OUT_OF_SAMPLE_END_DATE,
        title_suffix=title_suffix_out,
        show_plot=True
    )

    PERFORMANCE_MODE = os.getenv('ROC_PERFORMANCE_MODE', 'balanced')

    if PERFORMANCE_MODE == 'fast':
        RUN_MC_SIMULATION = False
        N_SIMULATIONS = 0
    elif PERFORMANCE_MODE == 'balanced':
        RUN_MC_SIMULATION, N_SIMULATIONS = ask_user_for_monte_carlo()
    else:  # thorough
        RUN_MC_SIMULATION = True
        N_SIMULATIONS = 100

    SHOW_PLOT = True

    logger.info(f"Performance mode: {PERFORMANCE_MODE}")
    logger.info(f"Monte Carlo simulations: {N_SIMULATIONS if RUN_MC_SIMULATION else 'Disabled'}")

    if RUN_MC_SIMULATION:
        logger.info("Running Advanced Monte Carlo on In-Sample Hong Kong data...")

        gc.collect()
        print_memory_usage()

        run_advanced_monte_carlo(
            n_simulations=N_SIMULATIONS,
            trades_df=trades_df.copy(),
            market_data_df=market_data_df,
            start_date=IN_SAMPLE_START_DATE,
            end_date=IN_SAMPLE_END_DATE,
            initial_capital=INITIAL_CAPITAL,
            commission_rate=COMMISSION_RATE,
            stamp_duty_rate=STAMP_DUTY_RATE,
            max_position_percent=MAX_POSITION_PERCENT,
            show_plot=SHOW_PLOT
        )

        gc.collect()
        logger.info("Memory cleanup completed after Monte Carlo simulation")
        print_memory_usage()

    else:
        logger.info("Skipping Advanced Monte Carlo simulation")

    logger.info("Performing final memory cleanup...")
    gc.collect()
    print_memory_usage()

    print_section_header("Hong Kong Portfolio Backtest Completed Successfully!", level=1)

# --- Script Entry Point ---
if __name__ == "__main__":
    main()
