import sys
import os
import pandas as pd
from tqdm import tqdm
import glob

# Configure tqdm to work with pandas apply
tqdm.pandas()

# --- Path Setup ---
# Add the project root to the Python path to resolve import issues
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- Strategy Configuration ---
LOOKBACK_DAYS = 10

# --- Data Loading Function ---
def load_hshare_data(hshare_daily_path: str) -> (pd.DataFrame, dict):
    """Loads and combines all H-share daily data from individual CSV files."""
    all_hshare_files = glob.glob(os.path.join(hshare_daily_path, '*.csv'))
    if not all_hshare_files:
        print("No H-share data files found.")
        return pd.DataFrame(), {}

    df_list = []
    stock_names = {}
    print(f"Loading data for {len(all_hshare_files)} H-share stocks...")
    for f in tqdm(all_hshare_files, desc="Loading H-Shares"):
        try:
            df = pd.read_csv(f)
            if df.empty:
                continue
            
            stock_code_full = df.loc[0, 'code']
            stock_code = stock_code_full.split('.')[-1]
            stock_name = df.loc[0, 'name']
            
            df['StockCode'] = stock_code
            if stock_code not in stock_names:
                stock_names[stock_code] = stock_name

            df.rename(columns={'time_key': 'Date', 'close': 'Close'}, inplace=True)
            df_list.append(df[['Date', 'StockCode', 'Close']])
        except Exception as e:
            print(f"Error processing H-share file {f}: {e}")
            continue
    
    if not df_list:
        return pd.DataFrame(), {}

    combined_df = pd.concat(df_list, ignore_index=True)
    return combined_df, stock_names

# --- Core Screener Function ---
def check_entry_condition(stock_df: pd.DataFrame, params: dict) -> pd.DataFrame:
    """
    Checks if a single stock meets the entry criteria within the lookback period.
    """
    stock_df = stock_df.sort_index()
    if len(stock_df) < params['roc_std_window'] + 5:
        return None

    roc40 = (stock_df['Close'] / stock_df['Close'].shift(40) - 1) * 100
    roc40_prev = roc40.shift(1)
    roc40_std = roc40.rolling(window=params['roc_std_window']).std()
    threshold40 = -(roc40_std * 1.7)
    
    roc30 = (stock_df['Close'] / stock_df['Close'].shift(30) - 1) * 100
    roc30_prev = roc30.shift(1)
    roc30_std = roc30.rolling(window=params['roc_std_window']).std()
    threshold30 = -(roc30_std * 2.0)
    
    trigger_roc40_thresh = (roc40 < threshold40) & (roc40 > roc40_prev)
    trigger_roc30_thresh = (roc30 < threshold30) & (roc30 > roc30_prev)
    trigger_roc_abs = (roc40 < -20) & (roc40 > roc40_prev)

    is_triggered = trigger_roc40_thresh | trigger_roc30_thresh | trigger_roc_abs

    recent_triggers = is_triggered.tail(params['lookback_days'])
    triggered_dates = recent_triggers[recent_triggers].index

    if not triggered_dates.empty:
        results = []
        for trade_date in triggered_dates:
            roc40_val, threshold40_val = roc40.get(trade_date), threshold40.get(trade_date)
            roc30_val, threshold30_val = roc30.get(trade_date), threshold30.get(trade_date)
            
            if pd.isna(roc40_val) or pd.isna(threshold40_val) or pd.isna(roc30_val) or pd.isna(threshold30_val):
                continue

            reasons = []
            if trigger_roc40_thresh.get(trade_date):
                reasons.append("1.7x ROC 40")
            elif trigger_roc_abs.get(trade_date):
                reasons.append("ROC < -20")
            if trigger_roc30_thresh.get(trade_date):
                reasons.append("2x ROC 30")

            if not reasons: continue

            results.append({
                'Date': trade_date.strftime('%Y-%m-%d'),
                'Close': stock_df.loc[trade_date, 'Close'],
                'Reason': ", ".join(reasons),
                'ROC(40)': f"{roc40_val:.2f}%", 'Thresh(40)': f"{threshold40_val:.2f}%",
                'ROC(30)': f"{roc30_val:.2f}%", 'Thresh(30)': f"{threshold30_val:.2f}%",
            })
        
        return pd.DataFrame(results) if results else None
    return None

def find_potential_hshare_trades(strategy_params: dict):
    """
    Scans the H-share market to find stocks meeting entry criteria.
    """
    hshare_df, hshare_names = load_hshare_data(
        os.path.join(project_root, 'hshare', 'H_daily')
    )

    if hshare_df.empty:
        print("No H-share data found.")
        return

    print("H-share data loaded. Preparing for analysis...")
    hshare_df['Date'] = pd.to_datetime(hshare_df['Date'])
    hshare_df.set_index('Date', inplace=True)

    print(f"Screening {hshare_df['StockCode'].nunique()} H-share stocks for signals...")
    potential_trades = hshare_df.groupby('StockCode').progress_apply(check_entry_condition, params=strategy_params, include_groups=False)
    
    if isinstance(potential_trades, pd.Series) and potential_trades.empty:
        potential_trades = pd.DataFrame()

    if not potential_trades.empty:
        potential_trades.reset_index(inplace=True)
        potential_trades.rename(columns={'level_0': 'StockCode'}, inplace=True)
        if 'level_1' in potential_trades.columns:
            potential_trades.drop(columns=['level_1'], inplace=True)

        potential_trades.sort_values(by='Date', ascending=False, inplace=True)
        potential_trades['StockName'] = potential_trades['StockCode'].map(hshare_names)
        
        potential_trades = potential_trades[[
            'StockCode', 'StockName', 'Date', 'Close', 'Reason', 
            'ROC(40)', 'Thresh(40)', 'ROC(30)', 'Thresh(30)'
        ]]

    print("\n" + "="*80)
    print(f"           Potential H-Share Trades Found in the Last {strategy_params['lookback_days']} Days")
    print("="*80)

    if potential_trades.empty:
        print("No stocks met the entry criteria in the lookback period.")
    else:
        print(potential_trades.to_string(index=False))
        print("\n" + "-"*80)
        print(f"Found {len(potential_trades)} potential trades.")

    print("="*80)

if __name__ == "__main__":
    strategy_params = {
        'roc_std_window': 750,
        'lookback_days': LOOKBACK_DAYS
    }
    find_potential_hshare_trades(strategy_params)
