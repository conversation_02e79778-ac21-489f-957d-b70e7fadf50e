#!/usr/bin/env python3
"""
RoC Strategy Optimization Runner

This script provides an easy interface to run parameter optimization for the RoC strategy.
It includes preset configurations for different optimization scenarios.

Usage:
    python run_optimization.py [--preset quick|balanced|thorough|custom]
"""

import sys
import os
import argparse
import subprocess
from datetime import datetime

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def print_banner():
    """Print a nice banner for the optimization runner."""
    print("="*70)
    print("  RoC Strategy Parameter Optimization Runner")
    print("  Maximize In-Sample Sharpe Ratio")
    print("="*70)
    print()

def get_optimization_presets():
    """Define optimization presets for different scenarios."""
    return {
        'quick': {
            'method': 'random',
            'n_trials': 20,
            'description': 'Quick optimization with 20 random trials (~30 minutes)',
            'estimated_time': '30 minutes'
        },
        'balanced': {
            'method': 'bayesian',
            'n_trials': 50,
            'description': 'Balanced optimization with 50 Bayesian trials (~2 hours)',
            'estimated_time': '2 hours'
        },
        'thorough': {
            'method': 'bayesian',
            'n_trials': 100,
            'description': 'Thorough optimization with 100 Bayesian trials (~4 hours)',
            'estimated_time': '4 hours'
        },
        'grid_small': {
            'method': 'grid',
            'n_trials': 100,
            'description': 'Small grid search with 100 combinations (~3 hours)',
            'estimated_time': '3 hours'
        }
    }

def show_presets():
    """Display available optimization presets."""
    presets = get_optimization_presets()
    
    print("Available Optimization Presets:")
    print("-" * 50)
    
    for name, config in presets.items():
        print(f"{name.upper()}:")
        print(f"  Method: {config['method']}")
        print(f"  Trials: {config['n_trials']}")
        print(f"  Description: {config['description']}")
        print(f"  Estimated Time: {config['estimated_time']}")
        print()

def run_optimization(method, n_trials):
    """Run the optimization with specified parameters."""
    script_path = os.path.join(os.path.dirname(__file__), 'optimize_roc_parameters.py')
    
    cmd = [
        sys.executable, script_path,
        '--method', method,
        '--n_trials', str(n_trials)
    ]
    
    print(f"Starting optimization...")
    print(f"Method: {method}")
    print(f"Trials: {n_trials}")
    print(f"Command: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        # Run the optimization script
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\nOptimization completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\nOptimization failed with error code {e.returncode}")
        return False
    except KeyboardInterrupt:
        print("\nOptimization interrupted by user")
        return False

def get_user_confirmation(preset_config):
    """Get user confirmation before starting optimization."""
    print(f"You selected: {preset_config['description']}")
    print(f"Estimated time: {preset_config['estimated_time']}")
    print()
    
    while True:
        response = input("Do you want to proceed? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            return True
        elif response in ['n', 'no']:
            return False
        else:
            print("Please enter 'y' or 'n'")

def custom_optimization():
    """Allow user to specify custom optimization parameters."""
    print("Custom Optimization Setup")
    print("-" * 30)
    
    # Get method
    while True:
        method = input("Choose method (random/bayesian/grid): ").lower().strip()
        if method in ['random', 'bayesian', 'grid']:
            break
        print("Please choose from: random, bayesian, grid")
    
    # Get number of trials
    while True:
        try:
            n_trials = int(input("Number of trials (10-200): "))
            if 10 <= n_trials <= 200:
                break
            print("Please enter a number between 10 and 200")
        except ValueError:
            print("Please enter a valid number")
    
    # Estimate time
    time_per_trial = 2  # minutes per trial (rough estimate)
    estimated_minutes = n_trials * time_per_trial
    estimated_hours = estimated_minutes / 60
    
    print(f"\nCustom configuration:")
    print(f"  Method: {method}")
    print(f"  Trials: {n_trials}")
    print(f"  Estimated time: {estimated_hours:.1f} hours")
    
    if get_user_confirmation({'description': f'Custom {method} optimization', 'estimated_time': f'{estimated_hours:.1f} hours'}):
        return method, n_trials
    else:
        return None, None

def check_prerequisites():
    """Check if all prerequisites are met."""
    print("Checking prerequisites...")
    
    # Check if required files exist
    required_files = [
        'strategies/RoC/run_full_market_backtest_RoC.py',
        'strategies/RoC/run_portfolio_backtest_RoC.py',
        'ashare/all_daily_hfq.parquet',
        'ashare/Indices_daily/sh000300.csv'
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = os.path.join(project_root, file_path)
        if not os.path.exists(full_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("ERROR: Missing required files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    print("✓ All required files found")
    return True

def main():
    parser = argparse.ArgumentParser(description='Run RoC strategy parameter optimization')
    parser.add_argument('--preset', choices=['quick', 'balanced', 'thorough', 'grid_small', 'custom'],
                       default=None, help='Optimization preset to use')
    parser.add_argument('--list-presets', action='store_true',
                       help='List available presets and exit')
    
    args = parser.parse_args()
    
    print_banner()
    
    if args.list_presets:
        show_presets()
        return
    
    # Check prerequisites
    if not check_prerequisites():
        print("\nPlease ensure all required files are available before running optimization.")
        return
    
    presets = get_optimization_presets()
    
    # Determine optimization parameters
    if args.preset:
        if args.preset == 'custom':
            method, n_trials = custom_optimization()
            if method is None:
                print("Custom optimization cancelled.")
                return
        else:
            preset_config = presets[args.preset]
            method = preset_config['method']
            n_trials = preset_config['n_trials']
            
            if not get_user_confirmation(preset_config):
                print("Optimization cancelled.")
                return
    else:
        # Interactive mode - show presets and let user choose
        show_presets()
        
        while True:
            choice = input("Choose a preset (quick/balanced/thorough/grid_small/custom): ").lower().strip()
            if choice in presets:
                preset_config = presets[choice]
                method = preset_config['method']
                n_trials = preset_config['n_trials']
                
                if get_user_confirmation(preset_config):
                    break
                else:
                    print("Please choose again or press Ctrl+C to exit.")
            elif choice == 'custom':
                method, n_trials = custom_optimization()
                if method is not None:
                    break
                else:
                    print("Please choose again or press Ctrl+C to exit.")
            else:
                print("Invalid choice. Please try again.")
    
    # Run optimization
    print(f"\nStarting optimization at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    success = run_optimization(method, n_trials)
    
    if success:
        print(f"\nOptimization completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\nNext steps:")
        print("1. Review the optimization results in strategies/RoC/utilities/optimization_results/")
        print("2. Update the strategy parameters in run_full_market_backtest_RoC.py")
        print("3. Run the updated strategy to verify improvements")
    else:
        print("\nOptimization failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
