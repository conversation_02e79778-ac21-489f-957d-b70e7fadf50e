import pandas as pd
import matplotlib.pyplot as plt
import os
import sys

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from utilities.complete_font_solution import setup_chinese_font

def plot_relative_strength(project_root):
    """
    Plots the relative strength of the strategy NAV against the CSI 300 benchmark.
    """
    # --- Define Output Directory ---
    output_dir = os.path.join(project_root, 'strategies', 'RoC', 'backtest_results')
    os.makedirs(output_dir, exist_ok=True)

    # --- Load Data ---
    nav_history_path = os.path.join(output_dir, 'nav_history.csv')
    benchmark_path = os.path.join(project_root, 'ashare', 'Indices_daily', 'sh000300.csv')

    try:
        strategy_nav = pd.read_csv(nav_history_path, index_col=0, parse_dates=True)['NAV']
        benchmark_data = pd.read_csv(benchmark_path, index_col='日期', parse_dates=True)
    except FileNotFoundError as e:
        print(f"Error: Could not find data file: {e}")
        print("Please run the backtest first to generate the nav_history.csv file.")
        return

    # --- Data Alignment and Calculation ---
    # Align benchmark data to strategy dates
    benchmark_close = benchmark_data['收盘'].reindex(strategy_nav.index).ffill()

    # Normalize both series to start at 1
    strategy_normalized = strategy_nav / strategy_nav.iloc[0]
    benchmark_normalized = benchmark_close / benchmark_close.iloc[0]

    # Calculate relative strength
    relative_strength = strategy_normalized / benchmark_normalized

    # --- Save Data to CSV ---
    output_df = pd.DataFrame({
        'Strategy_Normalized': strategy_normalized,
        'Benchmark_Normalized': benchmark_normalized,
        'Relative_Strength': relative_strength
    })
    output_df.index.name = 'Date'
    csv_output_path = os.path.join(output_dir, 'relative_strength_data.csv')
    output_df.to_csv(csv_output_path)
    print(f"Relative strength data saved to: {csv_output_path}")

    # --- Plotting ---
    font_prop = setup_chinese_font()
    plt.style.use('seaborn-v0_8-whitegrid')
    fig, ax = plt.subplots(figsize=(15, 8))

    ax.plot(relative_strength.index, relative_strength, label='策略/沪深300 相对强弱', color='#2c3e50')

    # Formatting
    ax.set_title('策略 vs. 沪深300 相对强弱曲线', fontproperties=font_prop, fontsize=18)
    ax.set_xlabel('日期', fontproperties=font_prop, fontsize=12)
    ax.set_ylabel('相对强弱比率', fontproperties=font_prop, fontsize=12)
    ax.legend(loc='upper left', prop=font_prop, fontsize=10)
    ax.grid(True, which='both', linestyle='--', linewidth=0.5)
    
    # Add a horizontal line at 1.0 for reference
    # The legend for axhline needs to be handled by passing the label and then calling ax.legend()
    ax.axhline(1.0, color='red', linestyle='--', linewidth=1)

    # Create a combined legend
    handles, labels = ax.get_legend_handles_labels()
    # Manually add the axhline label
    labels[0] = '策略/沪深300 相对强弱'
    handles.append(plt.Line2D([0], [0], color='red', linestyle='--', linewidth=1))
    labels.append('基准线 (1.0)')
    ax.legend(handles=handles, labels=labels, loc='upper left', prop=font_prop)

    plt.tight_layout()

    # --- Save the Plot ---
    output_dir = os.path.join(project_root, 'strategies', 'RoC', 'backtest_results')
    os.makedirs(output_dir, exist_ok=True)
    plot_path = os.path.join(output_dir, 'relative_strength_vs_csi300.png')
    plt.savefig(plot_path, dpi=300)
    print(f"Relative strength plot saved to: {plot_path}")
    
    plt.show()

if __name__ == "__main__":
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    plot_relative_strength(project_root)
