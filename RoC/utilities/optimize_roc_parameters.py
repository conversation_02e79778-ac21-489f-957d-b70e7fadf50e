#!/usr/bin/env python3
"""
RoC Strategy Parameter Optimization Script

This script optimizes the parameters of the RoC strategy to maximize in-sample Sharpe ratio.
It tests different parameter combinations using various optimization methods and saves results.

Usage:
    python optimize_roc_parameters.py [--method grid|random|bayesian] [--n_trials 50]
"""

import sys
import os
import pandas as pd
import numpy as np
import json
import argparse
from datetime import datetime
from itertools import product
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import strategy modules
from strategies.RoC.run_full_market_backtest_RoC import run_backtest_with_params
from strategies.RoC.run_portfolio_backtest_RoC import run_portfolio_backtest, add_stock_code_prefix

class RoCParameterOptimizer:
    """
    Parameter optimizer for RoC strategy focused on maximizing in-sample Sharpe ratio.
    """
    
    def __init__(self, results_dir=None):
        self.project_root = project_root
        self.results_dir = results_dir or os.path.join(
            self.project_root, 'strategies', 'RoC', 'utilities', 'optimization_results'
        )
        os.makedirs(self.results_dir, exist_ok=True)
        
        # Load market data once for efficiency
        self._load_market_data()
        
        # Optimization results storage
        self.results = []
        self.best_params = None
        self.best_sharpe = -np.inf
        
    def _load_market_data(self):
        """Load market data and benchmark data once for reuse."""
        print("Loading market data for optimization...")
        
        # Load market OHLC data
        market_data_path = os.path.join(self.project_root, 'ashare', 'all_daily_hfq.parquet')
        required_columns = ['日期', '股票代码', '收盘', '开盘', '最高', '最低', '成交量']
        self.market_data_df = pd.read_parquet(market_data_path, columns=required_columns)
        
        self.market_data_df = self.market_data_df.rename(columns={
            '日期': 'Date', '股票代码': 'StockCode', '收盘': 'Close',
            '开盘': 'Open', '最高': 'High', '最低': 'Low', '成交量': 'Volume'
        })
        self.market_data_df = add_stock_code_prefix(self.market_data_df)
        
        # Load benchmark data (CSI 300)
        benchmark_path = os.path.join(self.project_root, 'ashare', 'Indices_daily', 'sh000300.csv')
        self.benchmark_data_df = pd.read_csv(benchmark_path, index_col='日期', parse_dates=True)
        self.benchmark_data_df.rename(columns={'收盘': 'Close'}, inplace=True)
        
        print(f"Loaded {len(self.market_data_df):,} market data records")
        
    def define_parameter_space(self):
        """Define the parameter space for optimization."""
        return {
            'roc_period': [20, 25, 30, 35, 40],
            'roc_std_window': [500, 600, 750, 900, 1000],
            'roc_std_multiplier': [1.5, 1.75, 2.0, 2.25, 2.5, 2.75, 3.0],
            'profit_target_atr_multiplier': [1.5, 1.75, 2.0, 2.25, 2.5, 2.75, 3.0],
            'stop_loss_atr_multiplier': [1.0, 1.25, 1.5, 1.75, 2.0],
            'max_hold_days': [20, 25, 30, 35, 40]
        }
    
    def evaluate_parameters(self, params):
        """
        Evaluate a single parameter combination and return Sharpe ratio.
        
        Args:
            params (dict): Parameter dictionary
            
        Returns:
            dict: Results including Sharpe ratio and other metrics
        """
        try:
            # Step 1: Run full market backtest with these parameters
            strategy_params = {
                'roc_period': params['roc_period'],
                'roc_std_window': params['roc_std_window'],
                'roc_std_multiplier': params['roc_std_multiplier'],
                'atr_period': 14,  # Keep fixed
                'profit_target_atr_multiplier': params['profit_target_atr_multiplier'],
                'stop_loss_atr_multiplier': params['stop_loss_atr_multiplier'],
                'max_hold_days': params['max_hold_days']
            }
            
            # Run full market backtest (this generates trades CSV)
            trades_csv_path = run_backtest_with_params(
                strategy_params, 
                start_date='2010-01-01', 
                end_date='2023-12-31'  # In-sample period
            )
            
            # Step 2: Load the generated trades
            if not os.path.exists(trades_csv_path):
                return {'sharpe': -np.inf, 'error': 'Trades file not found'}
                
            trades_df = pd.read_csv(trades_csv_path, dtype={'StockCode': str})
            trades_df = add_stock_code_prefix(trades_df)
            
            if len(trades_df) == 0:
                return {'sharpe': -np.inf, 'error': 'No trades generated'}
            
            # Step 3: Run portfolio backtest to get Sharpe ratio
            portfolio_results = run_portfolio_backtest(
                trades_df=trades_df.copy(),
                market_data_df=self.market_data_df,
                benchmark_data_df=self.benchmark_data_df,
                max_position_percent=0.05,
                start_date='2010-01-01',
                end_date='2023-12-31',
                title_suffix="optimization",
                show_plot=False  # Don't show plots during optimization
            )
            
            # Extract key metrics
            sharpe = portfolio_results.get('sharpe', -np.inf)
            cagr = portfolio_results.get('cagr', -1)
            max_drawdown = portfolio_results.get('max_drawdown', -1)
            
            return {
                'sharpe': sharpe,
                'cagr': cagr,
                'max_drawdown': max_drawdown,
                'num_trades': len(trades_df),
                'error': None
            }
            
        except Exception as e:
            print(f"Error evaluating parameters {params}: {str(e)}")
            return {'sharpe': -np.inf, 'error': str(e)}
    
    def grid_search(self, max_combinations=None):
        """
        Perform grid search optimization.
        
        Args:
            max_combinations (int): Maximum number of combinations to test
        """
        print("Starting Grid Search Optimization...")
        param_space = self.define_parameter_space()
        
        # Generate all combinations
        param_names = list(param_space.keys())
        param_values = list(param_space.values())
        all_combinations = list(product(*param_values))
        
        if max_combinations and len(all_combinations) > max_combinations:
            print(f"Limiting to {max_combinations} random combinations from {len(all_combinations)} total")
            np.random.shuffle(all_combinations)
            all_combinations = all_combinations[:max_combinations]
        
        print(f"Testing {len(all_combinations)} parameter combinations...")
        
        for i, combination in enumerate(tqdm(all_combinations, desc="Grid Search")):
            params = dict(zip(param_names, combination))
            
            # Evaluate parameters
            result = self.evaluate_parameters(params)
            
            # Store result
            result_record = {
                'trial': i,
                'params': params,
                'timestamp': datetime.now().isoformat(),
                **result
            }
            self.results.append(result_record)
            
            # Update best parameters
            if result['sharpe'] > self.best_sharpe:
                self.best_sharpe = result['sharpe']
                self.best_params = params.copy()
                print(f"New best Sharpe: {self.best_sharpe:.4f} with params: {params}")
            
            # Save intermediate results every 10 trials
            if (i + 1) % 10 == 0:
                self._save_intermediate_results()
    
    def random_search(self, n_trials=100):
        """
        Perform random search optimization.
        
        Args:
            n_trials (int): Number of random trials to perform
        """
        print(f"Starting Random Search Optimization with {n_trials} trials...")
        param_space = self.define_parameter_space()
        
        for i in tqdm(range(n_trials), desc="Random Search"):
            # Generate random parameters
            params = {}
            for param_name, param_values in param_space.items():
                params[param_name] = np.random.choice(param_values)
            
            # Evaluate parameters
            result = self.evaluate_parameters(params)
            
            # Store result
            result_record = {
                'trial': i,
                'params': params,
                'timestamp': datetime.now().isoformat(),
                **result
            }
            self.results.append(result_record)
            
            # Update best parameters
            if result['sharpe'] > self.best_sharpe:
                self.best_sharpe = result['sharpe']
                self.best_params = params.copy()
                print(f"New best Sharpe: {self.best_sharpe:.4f} with params: {params}")
            
            # Save intermediate results every 10 trials
            if (i + 1) % 10 == 0:
                self._save_intermediate_results()
    
    def _save_intermediate_results(self):
        """Save intermediate results to prevent data loss."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_path = os.path.join(self.results_dir, f'optimization_results_{timestamp}.json')
        
        with open(results_path, 'w') as f:
            json.dump({
                'best_params': self.best_params,
                'best_sharpe': self.best_sharpe,
                'results': self.results
            }, f, indent=2)
    
    def save_final_results(self):
        """Save final optimization results."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save detailed results as JSON
        results_path = os.path.join(self.results_dir, f'final_optimization_results_{timestamp}.json')
        with open(results_path, 'w') as f:
            json.dump({
                'best_params': self.best_params,
                'best_sharpe': self.best_sharpe,
                'total_trials': len(self.results),
                'results': self.results
            }, f, indent=2)
        
        # Save results as CSV for easy analysis
        csv_path = os.path.join(self.results_dir, f'optimization_results_{timestamp}.csv')
        results_df = pd.DataFrame(self.results)
        
        # Flatten params dictionary into separate columns
        if not results_df.empty:
            params_df = pd.json_normalize(results_df['params'])
            results_df = pd.concat([results_df.drop('params', axis=1), params_df], axis=1)
        
        results_df.to_csv(csv_path, index=False)
        
        print(f"\nOptimization completed!")
        print(f"Results saved to: {results_path}")
        print(f"CSV results saved to: {csv_path}")
        print(f"Best Sharpe ratio: {self.best_sharpe:.4f}")
        print(f"Best parameters: {self.best_params}")
        
        return results_path, csv_path

    def bayesian_optimization(self, n_trials=50):
        """
        Perform Bayesian optimization using scikit-optimize.

        Args:
            n_trials (int): Number of trials to perform
        """
        try:
            from skopt import gp_minimize
            from skopt.space import Integer, Real
            from skopt.utils import use_named_args
        except ImportError:
            print("scikit-optimize not available. Please install with: pip install scikit-optimize")
            print("Falling back to random search...")
            return self.random_search(n_trials)

        print(f"Starting Bayesian Optimization with {n_trials} trials...")

        # Define search space for Bayesian optimization
        dimensions = [
            Integer(20, 40, name='roc_period'),
            Integer(500, 1000, name='roc_std_window'),
            Real(1.5, 3.0, name='roc_std_multiplier'),
            Real(1.5, 3.0, name='profit_target_atr_multiplier'),
            Real(1.0, 2.0, name='stop_loss_atr_multiplier'),
            Integer(20, 40, name='max_hold_days')
        ]

        @use_named_args(dimensions)
        def objective(**params):
            """Objective function for Bayesian optimization (minimize negative Sharpe)."""
            result = self.evaluate_parameters(params)

            # Store result
            result_record = {
                'trial': len(self.results),
                'params': params,
                'timestamp': datetime.now().isoformat(),
                **result
            }
            self.results.append(result_record)

            # Update best parameters
            if result['sharpe'] > self.best_sharpe:
                self.best_sharpe = result['sharpe']
                self.best_params = params.copy()
                print(f"New best Sharpe: {self.best_sharpe:.4f} with params: {params}")

            # Return negative Sharpe for minimization
            return -result['sharpe'] if result['sharpe'] != -np.inf else 1000

        # Run Bayesian optimization
        result = gp_minimize(
            func=objective,
            dimensions=dimensions,
            n_calls=n_trials,
            random_state=42,
            verbose=True
        )

        print(f"Bayesian optimization completed with best Sharpe: {self.best_sharpe:.4f}")

    def analyze_results(self):
        """Analyze optimization results and provide insights."""
        if not self.results:
            print("No results to analyze.")
            return

        print("\n" + "="*60)
        print("OPTIMIZATION RESULTS ANALYSIS")
        print("="*60)

        # Convert results to DataFrame for analysis
        results_df = pd.DataFrame(self.results)

        # Filter out failed trials
        valid_results = results_df[results_df['sharpe'] != -np.inf].copy()

        if valid_results.empty:
            print("No valid results found.")
            return

        print(f"Valid trials: {len(valid_results)}/{len(results_df)}")
        print(f"Success rate: {len(valid_results)/len(results_df)*100:.1f}%")

        # Sharpe ratio statistics
        print(f"\nSharpe Ratio Statistics:")
        print(f"  Best: {valid_results['sharpe'].max():.4f}")
        print(f"  Mean: {valid_results['sharpe'].mean():.4f}")
        print(f"  Std:  {valid_results['sharpe'].std():.4f}")
        print(f"  Median: {valid_results['sharpe'].median():.4f}")

        # Top 5 parameter combinations
        print(f"\nTop 5 Parameter Combinations:")
        top_5 = valid_results.nlargest(5, 'sharpe')
        for i, (_, row) in enumerate(top_5.iterrows(), 1):
            params = row['params'] if isinstance(row['params'], dict) else eval(row['params'])
            print(f"  {i}. Sharpe: {row['sharpe']:.4f}, CAGR: {row['cagr']:.2%}, "
                  f"MaxDD: {row['max_drawdown']:.2%}")
            print(f"     Params: {params}")

        # Parameter sensitivity analysis
        print(f"\nParameter Sensitivity Analysis:")
        param_names = list(self.define_parameter_space().keys())

        for param in param_names:
            if param in valid_results.columns:
                correlation = valid_results[param].corr(valid_results['sharpe'])
                print(f"  {param}: correlation with Sharpe = {correlation:.3f}")

    def generate_summary_report(self):
        """Generate a comprehensive summary report."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = os.path.join(self.results_dir, f'optimization_summary_{timestamp}.txt')

        with open(report_path, 'w') as f:
            f.write("RoC Strategy Parameter Optimization Summary Report\n")
            f.write("="*60 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write(f"Total trials: {len(self.results)}\n")
            f.write(f"Best Sharpe ratio: {self.best_sharpe:.4f}\n")
            f.write(f"Best parameters:\n")
            for param, value in self.best_params.items():
                f.write(f"  {param}: {value}\n")

            f.write(f"\nRecommendation:\n")
            f.write(f"Update the strategy_params in run_full_market_backtest_RoC.py with:\n")
            f.write(f"strategy_params = {{\n")
            for param, value in self.best_params.items():
                f.write(f"    '{param}': {value},\n")
            f.write(f"    'atr_period': 14,  # Keep existing value\n")
            f.write(f"}}\n")

        print(f"Summary report saved to: {report_path}")
        return report_path

def main():
    parser = argparse.ArgumentParser(description='Optimize RoC strategy parameters')
    parser.add_argument('--method', choices=['grid', 'random', 'bayesian'], default='random',
                       help='Optimization method to use')
    parser.add_argument('--n_trials', type=int, default=50,
                       help='Number of trials for optimization')
    parser.add_argument('--analyze', action='store_true',
                       help='Analyze existing results without running new optimization')

    args = parser.parse_args()

    # Initialize optimizer
    optimizer = RoCParameterOptimizer()

    if not args.analyze:
        # Run optimization
        if args.method == 'grid':
            optimizer.grid_search(max_combinations=args.n_trials)
        elif args.method == 'random':
            optimizer.random_search(n_trials=args.n_trials)
        elif args.method == 'bayesian':
            optimizer.bayesian_optimization(n_trials=args.n_trials)

        # Save final results
        optimizer.save_final_results()

    # Analyze results
    optimizer.analyze_results()

    # Generate summary report
    optimizer.generate_summary_report()

if __name__ == "__main__":
    main()
