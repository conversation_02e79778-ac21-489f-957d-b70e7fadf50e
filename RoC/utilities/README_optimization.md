# RoC Strategy Parameter Optimization

This directory contains tools for optimizing the RoC strategy parameters to maximize in-sample Sharpe ratio.

## Overview

The optimization system tests different parameter combinations for the RoC strategy and identifies the settings that produce the highest Sharpe ratio during the in-sample period (2010-2023). This helps improve strategy performance by finding optimal parameter values.

## Files

- `optimize_roc_parameters.py` - Core optimization engine
- `run_optimization.py` - User-friendly interface with presets
- `README_optimization.md` - This documentation file
- `optimization_results/` - Directory where results are saved

## Quick Start

### Option 1: Use Presets (Recommended)

```bash
# Run with interactive preset selection
python run_optimization.py

# Or specify a preset directly
python run_optimization.py --preset balanced

# List available presets
python run_optimization.py --list-presets
```

### Option 2: Direct Optimization

```bash
# Random search with 50 trials
python optimize_roc_parameters.py --method random --n_trials 50

# Bayesian optimization with 30 trials
python optimize_roc_parameters.py --method bayesian --n_trials 30

# Grid search with 100 combinations
python optimize_roc_parameters.py --method grid --n_trials 100
```

## Optimization Presets

| Preset | Method | Trials | Time | Description |
|--------|--------|--------|------|-------------|
| `quick` | Random | 20 | ~30 min | Fast exploration for initial insights |
| `balanced` | Bayesian | 50 | ~2 hours | Good balance of quality and speed |
| `thorough` | Bayesian | 100 | ~4 hours | Comprehensive optimization |
| `grid_small` | Grid | 100 | ~3 hours | Systematic grid search |

## Parameters Being Optimized

The optimization focuses on these key strategy parameters:

| Parameter | Current | Range | Description |
|-----------|---------|-------|-------------|
| `roc_period` | 30 | 20-40 | Period for Rate of Change calculation |
| `roc_std_window` | 750 | 500-1000 | Window for ROC standard deviation |
| `roc_std_multiplier` | 2.0 | 1.5-3.0 | Multiplier for ROC entry threshold |
| `profit_target_atr_multiplier` | 2.0 | 1.5-3.0 | ATR multiplier for profit target |
| `stop_loss_atr_multiplier` | 1.5 | 1.0-2.0 | ATR multiplier for stop loss |
| `max_hold_days` | 30 | 20-40 | Maximum holding period |

## Optimization Methods

### 1. Random Search
- **Best for**: Quick exploration, initial parameter insights
- **Pros**: Fast, good coverage of parameter space
- **Cons**: May miss optimal combinations

### 2. Bayesian Optimization
- **Best for**: Efficient optimization with limited trials
- **Pros**: Intelligent parameter selection, converges faster
- **Cons**: Requires scikit-optimize library

### 3. Grid Search
- **Best for**: Systematic exploration, reproducible results
- **Pros**: Comprehensive coverage, deterministic
- **Cons**: Computationally expensive

## Results and Analysis

### Output Files

After optimization, you'll find these files in `optimization_results/`:

1. **`final_optimization_results_YYYYMMDD_HHMMSS.json`** - Complete results with all trials
2. **`optimization_results_YYYYMMDD_HHMMSS.csv`** - Results in CSV format for analysis
3. **`optimization_summary_YYYYMMDD_HHMMSS.txt`** - Human-readable summary report

### Key Metrics

- **Sharpe Ratio**: Primary optimization target (in-sample)
- **CAGR**: Compound Annual Growth Rate
- **Max Drawdown**: Maximum portfolio decline
- **Number of Trades**: Total trades generated

### Interpreting Results

1. **Best Parameters**: Found in the summary report
2. **Parameter Sensitivity**: Correlation analysis shows which parameters matter most
3. **Top Combinations**: Multiple good parameter sets for robustness

## Implementation

### Applying Optimized Parameters

1. Review the optimization summary report
2. Update `strategy_params` in `run_full_market_backtest_RoC.py`:

```python
strategy_params = {
    'roc_period': 25,                    # Optimized value
    'roc_std_window': 600,               # Optimized value
    'roc_std_multiplier': 2.25,          # Optimized value
    'atr_period': 14,                    # Keep existing
    'profit_target_atr_multiplier': 2.5, # Optimized value
    'stop_loss_atr_multiplier': 1.25,    # Optimized value
    'max_hold_days': 35                  # Optimized value
}
```

3. Run the full market backtest to generate new trades
4. Run the portfolio backtest to verify improvements

### Validation

After applying optimized parameters:

1. **In-Sample Verification**: Confirm improved Sharpe ratio
2. **Out-of-Sample Testing**: Test on 2024+ data
3. **Robustness Check**: Ensure parameters work across different market conditions

## Prerequisites

### Required Files
- `strategies/RoC/run_full_market_backtest_RoC.py`
- `strategies/RoC/run_portfolio_backtest_RoC.py`
- `ashare/all_daily_hfq.parquet`
- `ashare/Indices_daily/sh000300.csv`

### Optional Dependencies
```bash
# For Bayesian optimization
pip install scikit-optimize

# For enhanced analysis
pip install matplotlib seaborn
```

## Performance Considerations

### Memory Usage
- Each trial loads full market data (~2GB)
- Monitor system memory during optimization
- Consider reducing `n_trials` if memory is limited

### Time Estimates
- ~2-3 minutes per trial (depends on system)
- Grid search: trials × 2-3 minutes
- Random/Bayesian: similar per trial

### Optimization Tips
1. Start with `quick` preset for initial insights
2. Use `balanced` for production optimization
3. Run `thorough` for final parameter selection
4. Consider multiple optimization runs for robustness

## Troubleshooting

### Common Issues

1. **Memory Error**: Reduce `n_trials` or close other applications
2. **No Trades Generated**: Check parameter ranges aren't too restrictive
3. **Import Errors**: Ensure all required modules are installed
4. **File Not Found**: Verify all prerequisite files exist

### Getting Help

1. Check the console output for detailed error messages
2. Review the intermediate results files for partial progress
3. Use `--analyze` flag to analyze existing results without re-running

## Example Workflow

```bash
# 1. Quick exploration
python run_optimization.py --preset quick

# 2. Review initial results
cat optimization_results/optimization_summary_*.txt

# 3. Run thorough optimization
python run_optimization.py --preset thorough

# 4. Apply best parameters to strategy
# (Update run_full_market_backtest_RoC.py)

# 5. Verify improvements
cd ../..
python run_full_market_backtest_RoC.py
python run_portfolio_backtest_RoC.py
```

This optimization system helps systematically improve the RoC strategy performance by finding the parameter combination that maximizes in-sample Sharpe ratio.
