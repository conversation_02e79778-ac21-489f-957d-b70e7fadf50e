import sys
import os
import pandas as pd
import matplotlib.pyplot as plt

# Add the project root to the Python path to resolve import issues
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from utilities.complete_font_solution import setup_chinese_font
from strategies.RoC.roc_strategy import apply_roc_atr_strategy

def plot_backtest_results(df_with_prices, trades_df, code, name, output_dir):
    """
    Plots the backtest results, showing entry and exit points on the price chart.
    Saves the plot to a file.
    """
    if trades_df.empty:
        print("No trades to plot.")
        return

    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Set up Chinese fonts for the plot
    setup_chinese_font()

    plt.figure(figsize=(20, 10))
    plt.plot(df_with_prices.index, df_with_prices['Close'], label='收盘价', alpha=0.7, linewidth=1)

    # Plot entry and exit points
    plt.scatter(trades_df['EntryDate'], trades_df['EntryPrice'], 
                label='买入点', marker='^', color='green', s=120, zorder=5, edgecolors='black')
    plt.scatter(trades_df['ExitDate'], trades_df['ExitPrice'], 
                label='卖出点', marker='v', color='red', s=120, zorder=5, edgecolors='black')

    # Connect entry and exit for each trade
    for i, trade in trades_df.iterrows():
        plt.plot([trade['EntryDate'], trade['ExitDate']], 
                 [trade['EntryPrice'], trade['ExitPrice']], 
                 linestyle='--', color='gray', linewidth=0.8)

    plt.title(f'{name} ({code}) 回测交易点位图')
    plt.xlabel('日期')
    plt.ylabel('价格')
    plt.legend()
    plt.grid(True, which='both', linestyle='--', linewidth=0.5)
    plt.tight_layout()
    
    # Save the plot
    plot_filename = f"{code}_{name}_backtest.png".replace(" ", "_")
    plot_path = os.path.join(output_dir, plot_filename)
    plt.savefig(plot_path, dpi=300)
    plt.close()
    
    print(f"\n📈 回测图表已保存至: {plot_path}")


def run_backtest(code: str, name: str, is_index: bool = False, roc_params: dict = None):
    """
    Loads asset data, applies the ROC/ATR strategy, and prints the results.
    
    Args:
        code (str): The stock or index code.
        name (str): The name of the asset for display purposes.
        is_index (bool): True if the asset is an index, False for a stock.
        roc_params (dict): Parameters for the ROC strategy.
    """
    # 1. Determine file path
    if is_index:
        file_path = f"ashare/Indices_daily/{code}.csv"
    else:
        file_path = f"ashare/daily_hfq/{code}_daily_hfq.csv"

    # 2. Load data
    try:
        df = pd.read_csv(os.path.join(project_root, file_path))
    except FileNotFoundError:
        print(f"Error: Data file not found at {file_path}")
        return

    # 3. Prepare data
    df.rename(columns={
        '日期': 'Date',
        '最高': 'High',
        '最低': 'Low',
        '收盘': 'Close',
        '开盘': 'Open'
    }, inplace=True)

    required_cols = ['Date', 'High', 'Low', 'Close', 'Open']
    if not all(col in df.columns for col in required_cols):
        print(f"Error: The file {file_path} is missing required columns.")
        return
        
    df['Date'] = pd.to_datetime(df['Date'])
    df.set_index('Date', inplace=True)
    df.sort_index(inplace=True)

    print(f"--- Running ROC/ATR Strategy Backtest on {name} ({code}) ---")
    print(f"Data from {df.index.min().date()} to {df.index.max().date()}")
    print("-" * 70)

    # 4. Apply the strategy
    strategy_params = roc_params if roc_params else {}
    df_result, trades = apply_roc_atr_strategy(df, **strategy_params)

    # 5. Print results
    print("\nCompleted Trades Log:")
    if trades:
        trades_df = pd.DataFrame(trades)
        trades_df['EntryDate'] = pd.to_datetime(trades_df['EntryDate'])
        trades_df['ExitDate'] = pd.to_datetime(trades_df['ExitDate'])
        
        # Add Percentage PnL column
        trades_df['PnL_percentage'] = (trades_df['PnL_absolute'] / trades_df['EntryPrice']) * 100
        
        # Reorder columns for better display in the log
        if 'PnL_absolute' in trades_df.columns and 'PnL_percentage' in trades_df.columns:
            cols = trades_df.columns.tolist()
            pnl_abs_idx = cols.index('PnL_absolute')
            cols.insert(pnl_abs_idx + 1, cols.pop(cols.index('PnL_percentage')))
            trades_df = trades_df[cols]

        print(trades_df.to_string())
        
        # --- Performance Metrics in Percentage ---
        num_trades = len(trades_df)
        winning_trades = trades_df[trades_df['PnL_absolute'] > 0]
        num_winning_trades = len(winning_trades)
        losing_trades = trades_df[trades_df['PnL_absolute'] <= 0]
        num_losing_trades = len(losing_trades)
        
        win_rate = (num_winning_trades / num_trades) * 100 if num_trades > 0 else 0
        
        # Calculate compounded total return and other metrics based on percentage PnL
        if num_trades > 0:
            total_return_compounded = ((1 + trades_df['PnL_percentage'] / 100).prod() - 1) * 100
            avg_pnl_pct = trades_df['PnL_percentage'].mean()
        else:
            total_return_compounded = 0
            avg_pnl_pct = 0

        avg_profit_pct = winning_trades['PnL_percentage'].mean() if num_winning_trades > 0 else 0
        avg_loss_pct = losing_trades['PnL_percentage'].mean() if num_losing_trades > 0 else 0
        
        print(f"\n--- Performance Metrics for {name} ---")
        print(f"Total Compounded Return: {total_return_compounded:.2f}%")
        print(f"Number of Trades: {num_trades}")
        print(f"Winning Trades: {num_winning_trades}")
        print(f"Losing Trades: {num_losing_trades}")
        print(f"Win Rate: {win_rate:.2f}%")
        print(f"Average PnL per Trade: {avg_pnl_pct:.2f}%")
        if num_winning_trades > 0:
            print(f"Average Profit on Winning Trade: {avg_profit_pct:.2f}%")
        if num_losing_trades > 0:
            print(f"Average Loss on Losing Trade: {avg_loss_pct:.2f}%")
        
        if avg_loss_pct != 0 and num_winning_trades > 0:
            actual_rr_ratio = abs(avg_profit_pct / avg_loss_pct)
            print(f"Actual Average Risk/Reward Ratio: {actual_rr_ratio:.2f}:1")
        
        intended_reward = strategy_params.get('profit_target_atr_multiplier', 1.0)
        intended_risk = strategy_params.get('stop_loss_atr_multiplier', 2.5)
        print(f"Intended Reward/Risk Ratio (from params): {intended_reward / intended_risk:.2f}:1")

        # --- Plotting Results ---
        plot_output_dir = os.path.join(project_root, 'strategies', 'RoC', 'backtest_results')
        plot_backtest_results(df_result, trades_df, code, name, plot_output_dir)

    else:
        print("No trades were executed during this period.")
    print("\n" + "="*70 + "\n")


if __name__ == "__main__":
    # Define default parameters for the strategy
    default_params = {
        'roc_period': 14,
        'roc_std_window': 200,             # New: Lookback for ROC volatility
        'roc_std_multiplier': 2.5,           # New: Multiplier for the adaptive threshold, best practice is 2.5
        'atr_period': 14,
        'profit_target_atr_multiplier': 2.5, # Original: 1.0 ATR Profit Target, best practice is 2.5
        'stop_loss_atr_multiplier': 3.0,     # Original: 2.5 ATR Stop Loss, best practice is 3.0
        'max_hold_days': 40                  # best practice is 40 days of holding days
    }
    
    # --- Backtest on a Stock ---
    run_backtest(
        code="002475", 
        name="立讯精密", 
        is_index=False,
        roc_params=default_params
    )
    
    # --- Backtest on an Index ---
    run_backtest(
        code="sh000852", 
        name="中证1000指数", 
        is_index=True,
        roc_params=default_params
    )