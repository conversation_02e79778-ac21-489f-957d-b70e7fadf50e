import sys
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# --- Path Setup & Font ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)
from utilities.complete_font_solution import setup_chinese_font

def get_st_stock_list(project_root):
    """
    Loads all stock lists and returns a set of ST stock codes.
    A stock is considered ST if its name contains 'ST'.
    """
    st_stocks = set()
    list_files = ['sh_stock_list.csv', 'sz_stock_list.csv', 'bj_stock_list.csv']
    for file_name in list_files:
        try:
            path = os.path.join(project_root, 'ashare', file_name)
            df = pd.read_csv(path)
            # Find stocks where the '证券简称' column contains 'ST'
            st_df = df[df['证券简称'].str.contains('ST', na=False)]
            # Add the corresponding '证券代码' to our set
            st_stocks.update(st_df['证券代码'].astype(str).str.zfill(6))
        except FileNotFoundError:
            print(f"Warning: Stock list file not found at {path}")
        except Exception as e:
            print(f"An error occurred while processing {file_name}: {e}")
    return st_stocks

def analyze_worst_trades(trades_df, stock_data_df, num_trades=5):
    """
    Finds and analyzes the worst trades by PnL.
    For each, prints details and plots the stock price chart with trade markers.
    """
    worst_trades = trades_df.sort_values(by='PnL_pct', ascending=True).head(num_trades)
    
    print("\n" + "="*80)
    print(f"           Top {num_trades} Worst Trades Analysis (by PnL %)")
    print("="*80)
    
    setup_chinese_font()
    plt.style.use('seaborn-v0_8-whitegrid')
    
    for _, trade in worst_trades.iterrows():
        stock_code = trade['StockCode']
        entry_date = pd.to_datetime(trade['EntryDate'])
        exit_date = pd.to_datetime(trade['ExitDate'])
        entry_price = trade['EntryPrice']
        exit_price = trade['ExitPrice']
        pnl_percent = trade['PnL_pct']
        
        print(f"\n--- Trade: {stock_code} ---")
        print(f"Entry: {entry_date.date()} @ {entry_price:.2f}")
        print(f"Exit:  {exit_date.date()} @ {exit_price:.2f}")
        print(f"PnL:   {pnl_percent:.2f}%")
        print(f"Holding Period: {(exit_date - entry_date).days} days")
        
        # Plotting
        stock_chart_data = stock_data_df[stock_data_df['StockCode'] == stock_code].copy()
        stock_chart_data['Date'] = pd.to_datetime(stock_chart_data['Date'])
        
        # Define a context window for the plot (60 days before entry, 30 after exit)
        plot_start_date = entry_date - pd.Timedelta(days=60)
        plot_end_date = exit_date + pd.Timedelta(days=30)
        
        plot_data = stock_chart_data[
            (stock_chart_data['Date'] >= plot_start_date) & 
            (stock_chart_data['Date'] <= plot_end_date)
        ].set_index('Date')

        if plot_data.empty:
            print("Could not find sufficient data to plot for this trade.")
            continue
            
        plt.figure(figsize=(12, 6))
        plt.plot(plot_data.index, plot_data['Close'], label='Stock Price', color='black')
        
        plt.scatter(entry_date, entry_price, color='green', marker='^', s=150, label=f'Buy @ {entry_price:.2f}', zorder=5)
        plt.scatter(exit_date, exit_price, color='red', marker='v', s=150, label=f'Sell @ {exit_price:.2f}', zorder=5)
        
        plt.title(f'Worst Trade Example: {stock_code} ({pnl_percent:.2f}%)', fontsize=16)
        plt.ylabel('Price')
        plt.xlabel('Date')
        plt.legend()
        plt.grid(True)
        plt.show()

def analyze_signal_clustering(trades_df, benchmark_df, top_n_days=5):
    """
    Finds dates with the highest number of trade entries (signal clustering)
    and analyzes the collective outcome of trades initiated on those days.
    """
    signal_counts = trades_df['EntryDate'].value_counts().head(top_n_days)
    
    print("\n" + "="*80)
    print(f"           Signal Clustering Analysis (Top {top_n_days} Days)")
    print("="*80)

    benchmark_df['Date'] = pd.to_datetime(benchmark_df['Date'])
    benchmark_df = benchmark_df.set_index('Date')
    
    for date, count in signal_counts.items():
        entry_date = pd.to_datetime(date)
        
        trades_on_date = trades_df[trades_df['EntryDate'] == entry_date]
        
        avg_pnl = trades_on_date['PnL_pct'].mean()
        win_rate = (trades_on_date['PnL_pct'] > 0).mean()
        
        # Market context
        market_change_30d = benchmark_df['Close'].pct_change(30).loc[entry_date]
        
        print(f"\n--- Cluster on {entry_date.date()} ---")
        print(f"Number of Trades: {count}")
        print(f"Average PnL of this cohort: {avg_pnl:.2%}")
        print(f"Win Rate of this cohort: {win_rate:.2%}")
        print(f"CSI 300 30-day change at entry: {market_change_30d:.2%}")

if __name__ == "__main__":
    # Define paths
    trades_log_path = os.path.join(project_root, 'strategies', 'RoC', 'backtest_results', 'full_market_trades_v2.csv')
    market_data_path = os.path.join(project_root, 'ashare', 'all_daily_hfq.parquet')
    benchmark_path = os.path.join(project_root, 'ashare', 'Indices_daily', 'sh000300.csv')
    
    # Load data
    print("Loading data for analysis...")
    all_trades = pd.read_csv(trades_log_path, parse_dates=['EntryDate', 'ExitDate'])
    
    # Filter trades to match the backtest period to ensure analysis is relevant
    start_date = '2010-01-01'
    all_trades = all_trades[all_trades['EntryDate'] >= start_date].copy()

    # --- Filter out ST Stocks ---
    st_blacklist = get_st_stock_list(project_root)
    print(f"Identified {len(st_blacklist)} ST stocks to exclude.")
    initial_trade_count = len(all_trades)
    all_trades['StockCode'] = all_trades['StockCode'].astype(str).str.zfill(6)
    all_trades = all_trades[~all_trades['StockCode'].isin(st_blacklist)]
    print(f"Filtered out {initial_trade_count - len(all_trades)} trades involving ST stocks.")
    # -----------------------------
    
    # Reload market data with original column names
    stock_data = pd.read_parquet(market_data_path)
    stock_data.rename(columns={'日期': 'Date', '股票代码': 'StockCode', '收盘': 'Close'}, inplace=True)

    benchmark_data = pd.read_csv(benchmark_path)
    benchmark_data.rename(columns={'日期': 'Date', '收盘': 'Close'}, inplace=True)
    
    # --- Run Analyses ---
    analyze_worst_trades(all_trades, stock_data, num_trades=3)
    analyze_signal_clustering(all_trades, benchmark_data, top_n_days=5) 