import sys
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from utilities.complete_font_solution import setup_chinese_font
from utilities.utils import define_market_regime

def analyze_trade_distribution(trades_file: str, index_file: str, start_date: str = '2010-01-01'):
    """
    Analyzes the distribution and performance of trades across different market regimes.
    """
    print(f"--- Starting Market Regime Analysis from {start_date} ---")
    start_date_dt = pd.to_datetime(start_date)
    
    # 1. Load data
    try:
        # Assuming PnL_pct is already in the CSV. If not, it needs to be calculated.
        trades_df = pd.read_csv(trades_file, parse_dates=['EntryDate', 'ExitDate'])
        index_df = pd.read_csv(index_file, parse_dates=['日期'])
    except FileNotFoundError as e:
        print(f"[ERROR] Required data file not found: {e}")
        return
    except KeyError:
        print("[ERROR] 'PnL_pct' column not found in trades file. Please ensure it's calculated and present.")
        return

    print("Data loaded successfully.")
    if not trades_df.empty:
        print(f"原始交易数据最早的日期是: {trades_df['EntryDate'].min().strftime('%Y-%m-%d')}")
    else:
        print("交易日志为空。")

    # Filter dataframes by start date
    trades_df = trades_df[trades_df['EntryDate'] >= start_date_dt].copy()

    # 2. Prepare Index Data and Define Regimes
    index_df.rename(columns={'日期': 'Date', '收盘': 'Close'}, inplace=True)
    index_df = index_df[index_df['Date'] >= start_date_dt].copy()

    if trades_df.empty:
        print(f"[WARNING] No trades found after start date {start_date}. Aborting analysis.")
        return
    if index_df.empty:
        print(f"[WARNING] No index data found after start date {start_date}. Aborting analysis.")
        return
        
    index_df.set_index('Date', inplace=True)
    
    regime_df = define_market_regime(index_df)
    
    # 3. Merge Trades with Market Regimes
    # We merge based on the trade's entry date
    trades_with_regime = pd.merge(trades_df, regime_df, left_on='EntryDate', right_index=True, how='left')
    trades_with_regime.dropna(subset=['Regime'], inplace=True) # Drop trades where regime is not defined (e.g., at the start)

    print("Trades merged with market regimes.")
    
    # 4. Calculate Distribution, Frequency, and Performance Metrics
    regime_groups = trades_with_regime.groupby('Regime')
    
    # Performance calculation logic
    def calculate_performance(group):
        total_trades = len(group)
        if total_trades == 0:
            return pd.Series([0, 0, 0, 0], index=['胜率 (%)', '平均盈利 (%)', '平均亏损 (%)', '整体平均盈亏 (%)'])
        
        wins = group[group['PnL_pct'] > 0]
        losses = group[group['PnL_pct'] <= 0]
        
        win_rate = (len(wins) / total_trades) * 100
        avg_win = wins['PnL_pct'].mean()
        avg_loss = losses['PnL_pct'].mean()
        
        # Calculate overall average PnL percentage for the group
        overall_avg_pnl = group['PnL_pct'].mean()

        return pd.Series([win_rate, avg_win, avg_loss, overall_avg_pnl], index=['胜率 (%)', '平均盈利 (%)', '平均亏损 (%)', '整体平均盈亏 (%)'])

    performance_df = regime_groups.apply(calculate_performance, include_groups=False)
    
    # Total days and trade counts for frequency analysis
    regime_days = regime_df['Regime'].value_counts()
    trade_counts = trades_with_regime['Regime'].value_counts()
    
    # Calculate regime proportions
    total_days = regime_days.sum()
    regime_proportion = (regime_days / total_days) * 100

    # Combine into a single DataFrame for analysis
    analysis_df = pd.DataFrame({
        '市场占比 (%)': regime_proportion,
        '总交易日数 (Days)': regime_days,
        '策略触发次数 (Trades)': trade_counts
    }).fillna(0)
    
    analysis_df = analysis_df.join(performance_df).fillna(0)

    # Calculate annualized frequency and Reward/Risk Ratio
    analysis_df['年化触发频率 (Trades/Year)'] = (analysis_df['策略触发次数 (Trades)'] / analysis_df['总交易日数 (Days)']) * 252
    analysis_df['盈亏比 (Reward/Risk)'] = analysis_df['平均盈利 (%)'] / abs(analysis_df['平均亏损 (%)'])
    analysis_df.sort_values(by='年化触发频率 (Trades/Year)', ascending=False, inplace=True)

    print("\n" + "="*80)
    print("           策略在不同市场状态下的表现分析")
    print("="*80)
    display_cols = ['市场占比 (%)', '年化触发频率 (Trades/Year)', '策略触发次数 (Trades)', '整体平均盈亏 (%)', '胜率 (%)', '盈亏比 (Reward/Risk)', '平均盈利 (%)', '平均亏损 (%)']
    print(analysis_df[display_cols].round(2).to_string())
    print("\n" + "-" * 80)
    
    # 5. Visualization
    setup_chinese_font()
    fig, axes = plt.subplots(4, 1, figsize=(12, 24), sharex=True)
    fig.suptitle('策略在不同市场状态下的表现分析', fontsize=18, y=0.95)
    
    # Plot 1: Annualized Trade Frequency
    sns.barplot(x=analysis_df.index, y=analysis_df['年化触发频率 (Trades/Year)'], hue=analysis_df.index, palette='viridis', ax=axes[0], legend=False)
    axes[0].set_title('年化触发频率', fontsize=14)
    axes[0].set_ylabel('年化交易次数')
    for index, value in enumerate(analysis_df['年化触发频率 (Trades/Year)']):
        axes[0].text(index, value, f"{value:.1f}", color='black', ha="center", va='bottom')

    # Plot 2: Win Rate
    sns.barplot(x=analysis_df.index, y=analysis_df['胜率 (%)'], hue=analysis_df.index, palette='plasma', ax=axes[1], legend=False)
    axes[1].set_title('胜率', fontsize=14)
    axes[1].set_ylabel('胜率 (%)')
    axes[1].axhline(50, color='grey', linestyle='--', linewidth=1) # 50% line
    for index, value in enumerate(analysis_df['胜率 (%)']):
        axes[1].text(index, value, f"{value:.1f}%", color='black', ha="center", va='bottom')
        
    # Plot 3: Reward/Risk Ratio
    sns.barplot(x=analysis_df.index, y=analysis_df['盈亏比 (Reward/Risk)'], hue=analysis_df.index, palette='magma', ax=axes[2], legend=False)
    axes[2].set_title('盈亏比', fontsize=14)
    axes[2].set_ylabel('盈亏比 (平均盈利 / 平均亏损)')
    axes[2].axhline(1, color='grey', linestyle='--', linewidth=1) # 1.0 line
    for index, value in enumerate(analysis_df['盈亏比 (Reward/Risk)']):
        axes[2].text(index, value, f"{value:.2f}", color='black', ha="center", va='bottom')

    # Plot 4: Overall Average PnL %
    sns.barplot(x=analysis_df.index, y=analysis_df['整体平均盈亏 (%)'], hue=analysis_df.index, palette='cividis', ax=axes[3], legend=False)
    axes[3].set_title('整体平均盈亏 (%)', fontsize=14)
    axes[3].set_ylabel('平均盈亏百分比 (%)')
    axes[3].axhline(0, color='grey', linestyle='--', linewidth=1)
    for index, value in enumerate(analysis_df['整体平均盈亏 (%)']):
        axes[3].text(index, value, f"{value:.2f}%", color='black', ha="center", va='bottom')

    plt.xlabel('市场状态 (Market Regime)', fontsize=12)
    plt.tight_layout(rect=[0, 0, 1, 0.95])
        
    # Save the plot
    plot_path = os.path.join(os.path.dirname(trades_file), 'regime_performance_analysis.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"分析图表已保存至: {plot_path}")
    
    plt.show()

# --- Script Entry Point ---
if __name__ == "__main__":
    trades_log_path = os.path.join(project_root, 'strategies', 'RoC', 'backtest_results', 'full_market_trades_v2.csv')
    market_index_path = os.path.join(project_root, 'ashare', 'Indices_daily', 'sh000300.csv')
    
    # 您可以在这里显式设置开始日期进行测试
    analyze_trade_distribution(trades_log_path, market_index_path, start_date='2010-01-01') 