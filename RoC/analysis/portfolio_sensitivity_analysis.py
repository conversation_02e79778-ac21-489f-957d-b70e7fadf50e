import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import random

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from utilities.utils import load_and_preprocess_all_market_data
from utilities.utils_indicators import rate_of_change, average_true_range
from utilities.complete_font_solution import setup_chinese_font
from utilities.utils_portfolio import Portfolio

# --- Configuration for Sensitivity Analysis ---
PARAM_TO_TEST = 'max_position_percentage'
PARAM_VALUES = [0.07]

# --- Portfolio Backtesting Engine ---

def get_entry_signals(df, params):
    """Pre-calculates all potential entry signals for all stocks."""
    roc = rate_of_change(df['Close'], period=params['roc_period'])
    # Use expanding window to avoid long warm-up period, ensuring signals from day one
    roc_std = roc.expanding(min_periods=params.get('roc_std_min_periods', 252)).std()
    adaptive_threshold = -(roc_std * params['roc_std_multiplier'])
    entry_condition = (roc < adaptive_threshold) & (roc.shift(1) < roc)
    return entry_condition

def run_portfolio_backtest(df_all, strategy_params, portfolio_params, desc=""):
    """
    Runs a portfolio-level backtest using the Portfolio class.
    This is a sequential process that iterates day-by-day.
    """
    # --- 1. Preparation ---
    print(f"Running portfolio backtest for: {desc}")
    
    # Set a MultiIndex for robust lookups and sort it for performance *before* signal calculation
    df_all = df_all.set_index(['StockCode'], append=True).swaplevel()
    df_all.sort_index(inplace=True)

    # Pre-calculate indicators on the entire dataset to ensure they are "warmed up"
    print("Calculating entry signals for all stocks...")
    
    signals = df_all.groupby('StockCode').apply(lambda x: get_entry_signals(x.droplevel(0), strategy_params), include_groups=False)
    atrs = df_all.groupby('StockCode').apply(lambda x: average_true_range(x.droplevel(0)['High'], x.droplevel(0)['Low'], x.droplevel(0)['Close'], period=strategy_params['atr_period']), include_groups=False)
    df_all['signal'] = signals
    df_all['atr'] = atrs
    
    # Filter data for the backtesting period *after* indicators have been calculated
    start_date = portfolio_params.get('start_date')
    if start_date:
        df_all = df_all[df_all.index.get_level_values('Date') >= pd.to_datetime(start_date)]
    
    # Get entry dates *after* filtering for the backtest period
    entry_dates = df_all[df_all['signal'] == True].index.get_level_values('Date').unique().sort_values()

    # 初始化 Portfolio 对象
    portfolio = Portfolio(
        initial_capital=portfolio_params['initial_capital'],
        commission_rate=portfolio_params.get('commission_rate', 0.0),
        stamp_duty_rate=portfolio_params.get('stamp_duty_rate', 0.0)
    )
    # 自定义头寸规模函数以匹配此脚本的逻辑
    max_pos_pct = portfolio_params['max_position_percentage']
    portfolio.position_sizing_fn = lambda nav, *args, **kwargs: nav * max_pos_pct
    
    # --- 2. Simulation Loop ---
    daily_nav = pd.Series(index=df_all.index.get_level_values('Date').unique().sort_values(), dtype=float)
    
    total_trades = 0
    stop_loss_exits, profit_target_exits, time_exits_win, time_exits_loss = 0, 0, 0, 0
    positions_info = {} # 存储止盈止损和时间退出等额外信息
    trade_log = [] # 用于记录每一笔交易

    print("Starting daily simulation...")
    for date in tqdm(daily_nav.index, desc="Simulating"):
        # --- A. Update portfolio value and handle exits ---
        exited_today = []
        
        # 创建当前持仓股票的最新价格字典
        held_stocks = list(portfolio.positions.keys())
        current_prices_dict = {}
        if held_stocks:
            try:
                # 批量获取所有持仓股的当日价格
                prices_today = df_all.loc[(held_stocks, date), 'Close']
                current_prices_dict = prices_today.to_dict()
            except KeyError:
                 # 如果某些股票当天没有数据，逐个获取
                for stock_code in held_stocks:
                    try:
                        current_prices_dict[stock_code] = df_all.loc[(stock_code, date), 'Close']
                    except KeyError:
                        # 如果没有当天价格，使用最后记录的入场价
                        current_prices_dict[stock_code] = portfolio.positions[stock_code]['entry_price']

        for stock_code, pos_details in list(portfolio.positions.items()):
            pos_info = positions_info[stock_code]
            exit_price = None
            exit_reason = None
            is_time_exit = False

            try:
                current_row = df_all.loc[(stock_code, date)]
                # 检查退出条件
                if current_row['Low'] <= pos_info['stop_loss']:
                    exit_price = pos_info['stop_loss']
                    exit_reason = 'Stop Loss'
                    stop_loss_exits += 1
                elif current_row['High'] >= pos_info['profit_target']:
                    exit_price = pos_info['profit_target']
                    exit_reason = 'Profit Target'
                    profit_target_exits += 1
                elif date >= pos_info['time_exit_date']:
                    exit_price = current_row['Close']
                    exit_reason = 'Time Exit'
                    is_time_exit = True
            except KeyError:
                # 停牌或数据缺失，检查时间退出
                if date >= pos_info['time_exit_date']:
                    exit_price = current_prices_dict.get(stock_code, pos_details['entry_price'])
                    exit_reason = 'Time Exit (No Data)'
                    is_time_exit = True

            if is_time_exit:
                if exit_price >= pos_details['entry_price']:
                    time_exits_win += 1
                else:
                    time_exits_loss += 1

            if exit_price is not None:
                trade = {
                    'StockCode': stock_code,
                    'EntryDate': pos_details['entry_date'],
                    'EntryPrice': pos_details['entry_price'],
                    'Shares': pos_details['shares'],
                    'ExitDate': date,
                    'ExitPrice': exit_price,
                    'ExitReason': exit_reason,
                    'PnL': (exit_price - pos_details['entry_price']) * pos_details['shares']
                }
                trade_log.append(trade)
                portfolio.execute_sell(stock_code, date, exit_price)
                exited_today.append(stock_code)

        for stock_code in exited_today:
            del positions_info[stock_code]
            
        # 在处理完卖出后，计算当天的投资组合净值
        nav_today, _ = portfolio.get_nav(current_prices_dict)
        daily_nav[date] = nav_today
        
        # --- B. Handle new entries ---
        if date in entry_dates:
            signals_today = df_all.loc[(slice(None), date), :][lambda x: x['signal']].copy()
            # 排除已持仓的股票
            signals_today = signals_today[~signals_today.index.get_level_values('StockCode').isin(portfolio.positions.keys())]
            
            shuffled_signals = signals_today.sample(frac=1)

            for idx, signal in shuffled_signals.iterrows():
                stock_code, _ = idx
                entry_price = signal['Close']
                if entry_price == 0: continue
                
                # 使用 Portfolio 类执行买入
                buy_successful = portfolio.execute_buy(stock_code, date, entry_price, nav_today)

                if buy_successful:
                    total_trades += 1
                    atr_at_entry = signal['atr']
                    if pd.isna(atr_at_entry): continue
                    
                    # 存储该仓位的止盈止损信息
                    positions_info[stock_code] = {
                        'stop_loss': entry_price - (strategy_params['stop_loss_atr_multiplier'] * atr_at_entry),
                        'profit_target': entry_price + (strategy_params['profit_target_atr_multiplier'] * atr_at_entry),
                        'time_exit_date': date + pd.Timedelta(days=strategy_params['max_hold_days'])
                    }
                else:
                    # 资金不足
                    break
    
    # --- 3. Calculate final statistics ---
    # Save the trade log from the last simulation run
    if trade_log:
        trades_df = pd.DataFrame(trade_log)
        log_output_path = os.path.join(
            project_root, 'strategies', 'RoC', 'sensitivity_analysis_results', 'dynamic_simulation_trades.csv'
        )
        trades_df.to_csv(log_output_path, index=False)
        print(f"\n交易日志已保存至: {log_output_path}")

    final_portfolio_value = daily_nav.iloc[-1]
    returns = daily_nav.pct_change().dropna()
    sharpe_ratio = (returns.mean() / returns.std()) * np.sqrt(252) if returns.std() > 0 else 0
    
    cumulative_returns = (1 + returns).cumprod()
    peak = cumulative_returns.cummax()
    drawdown = (cumulative_returns - peak) / peak
    max_drawdown = drawdown.min()
    
    total_exits = stop_loss_exits + profit_target_exits + time_exits_win + time_exits_loss

    return {
        "Final Portfolio Value": final_portfolio_value,
        "Total Return %": (final_portfolio_value / portfolio.initial_capital - 1) * 100,
        "Sharpe Ratio": sharpe_ratio,
        "Max Drawdown %": max_drawdown * 100,
        "# Trades Realized": total_exits,
        "% Stop-Loss": (stop_loss_exits / total_exits * 100) if total_exits > 0 else 0,
        "% Profit-Target": (profit_target_exits / total_exits * 100) if total_exits > 0 else 0,
        "% Time-Exit Win": (time_exits_win / total_exits * 100) if total_exits > 0 else 0,
        "% Time-Exit Loss": (time_exits_loss / total_exits * 100) if total_exits > 0 else 0
    }

def plot_sensitivity_analysis(results_df, param_name):
    """Plots the results of the portfolio sensitivity analysis."""
    setup_chinese_font()
    fig, ax1 = plt.subplots(figsize=(18, 10))
    param_title = param_name.replace("_", " ").title()
    colors = ['#4C72B0', '#55A868', '#C44E52', '#8172B2']

    # Bar for Final Portfolio Value
    ax1.bar(results_df.index.astype(str), results_df['Final Portfolio Value'], color='purple', alpha=0.7, label='最终投资组合价值')
    ax1.set_ylabel('最终投资组合价值 (千万)', color='purple', fontsize=14)
    ax1.tick_params(axis='y', labelcolor='purple')
    ax1.tick_params(axis='x', rotation=0)
    ax1.get_yaxis().set_major_formatter(plt.FuncFormatter(lambda x, p: format(int(x/1_000_000), ',')))


    # Axis 2 for Max Drawdown
    ax2 = ax1.twinx()
    ax2.plot(results_df.index.astype(str), results_df['Max Drawdown %'], color=colors[2], marker='s', label='最大回撤 (%)')
    ax2.set_ylabel('最大回撤 (%)', color=colors[2], fontsize=14)
    ax2.tick_params(axis='y', labelcolor=colors[2])
    
    # Axis 3 for Sharpe Ratio
    ax3 = ax1.twinx()
    ax3.spines['right'].set_position(('outward', 60))
    ax3.plot(results_df.index.astype(str), results_df['Sharpe Ratio'], color=colors[1], marker='o', label='夏普比率')
    ax3.set_ylabel('夏普比率', color=colors[1], fontsize=14)
    ax3.tick_params(axis='y', labelcolor=colors[1])


    # Legends
    lines, labels = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    lines3, labels3 = ax3.get_legend_handles_labels()
    ax1.legend(lines + lines2 + lines3, labels + labels2 + labels3, loc='upper center', bbox_to_anchor=(0.5, -0.1), ncol=3)

    plt.title(f'对 {param_title} 的投资组合敏感性分析', fontsize=18, pad=20)
    plt.grid(True)
    fig.tight_layout(rect=[0, 0.05, 1, 0.95])
    
    output_dir = os.path.join(project_root, 'strategies', 'RoC', 'sensitivity_analysis_results')
    os.makedirs(output_dir, exist_ok=True)
    filename = os.path.join(output_dir, f'portfolio_sensitivity_analysis_{param_name}.png')
    plt.savefig(filename)
    print(f"图表已保存至: {filename}")
    plt.show()

def main():
    base_strategy_params = {
        'roc_period': 40, # default  = 14, best = 40
        'roc_std_min_periods': 250, # default = 200, best = 250
        'roc_std_multiplier': 1.7, # default = 2.3, best = 1.7
        'atr_period': 14, # default = 14
        'profit_target_atr_multiplier': 2.5, # default = 1, best = 2.5
        'stop_loss_atr_multiplier': 2.0, # default = 2.5
        'max_hold_days': 30 # default =20
    }
    
    portfolio_params = {
        'initial_capital': 10_000_000,
        'max_position_percentage': 0.07,
        'num_simulations': 5, # Run only once to get a clean trade log
        'commission_rate': 0.0003,
        'stamp_duty_rate': 0.0005,
        'start_date': '2010-01-01' # Add start_date for consistency
    }
    
    # The parameter to test and its values are now defined as global constants
    # at the top of the script (PARAM_TO_TEST, PARAM_VALUES).

    print("--- 开始投资组合敏感性分析 (蒙特卡洛方法) ---")
    
    market_data_path = os.path.join(project_root, 'ashare', 'all_daily_hfq.parquet')
    df_all = load_and_preprocess_all_market_data(market_data_path)

    final_results = []
    num_simulations = portfolio_params.get('num_simulations', 1)

    for value in PARAM_VALUES:
        simulation_runs = []
        print(f"\n--- Testing {PARAM_TO_TEST} = {value} ({num_simulations} simulations) ---")
        for i in range(num_simulations):
            current_params = base_strategy_params.copy()
            current_params[PARAM_TO_TEST] = value
            
            desc = f"Sim {i+1}/{num_simulations} for {PARAM_TO_TEST}={value}"
            summary_stats = run_portfolio_backtest(df_all.copy(), current_params, portfolio_params, desc)
            simulation_runs.append(summary_stats)

        # Average the results from all simulation runs for this parameter value
        avg_results_df = pd.DataFrame(simulation_runs)
        averaged_stats = avg_results_df.mean().to_dict()
        averaged_stats[PARAM_TO_TEST] = value
        final_results.append(averaged_stats)

    results_df = pd.DataFrame(final_results).set_index(PARAM_TO_TEST)
    results_df.rename(columns={'Total Trades': '# Trades Realized'}, inplace=True)
    
    # 将总回报率的计算方式修正为基于平均最终投资组合价值
    if 'Final Portfolio Value' in results_df.columns:
        initial_capital = portfolio_params.get('initial_capital', 10_000_000)
        results_df['Total Return %'] = (results_df['Final Portfolio Value'] / initial_capital - 1) * 100

    # Save results to CSV for faster re-plotting
    results_output_path = os.path.join(project_root, 'strategies', 'RoC', 'sensitivity_analysis_results', f'portfolio_results_{PARAM_TO_TEST}.csv')
    results_df.to_csv(results_output_path)
    print(f"\n--- 投资组合敏感性分析平均结果 ---")
    print(results_df)

    plot_sensitivity_analysis(results_df, PARAM_TO_TEST)

def main_plot_only():
    """Function to regenerate the plot from saved results."""
    results_file = os.path.join(project_root, 'strategies', 'RoC', 'sensitivity_analysis_results', f'portfolio_results_{PARAM_TO_TEST}.csv')
    
    if os.path.exists(results_file):
        print(f"从 {results_file} 加载结果并重新生成图表...")
        results_df = pd.read_csv(results_file, index_col=PARAM_TO_TEST)
        print(results_df)
        plot_sensitivity_analysis(results_df, PARAM_TO_TEST)
    else:
        print("找不到结果文件。请先运行完整的分析。")


if __name__ == "__main__":
    # To run the full analysis:
    main()
    # To re-plot from the last results:
    # main_plot_only() 