import sys
import os
import pandas as pd
import matplotlib.pyplot as plt
from tqdm import tqdm
from multiprocessing import Pool, cpu_count
from functools import partial
import json

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from strategies.RoC.roc_strategy import apply_roc_atr_strategy
from utilities.complete_font_solution import setup_chinese_font

# --- Analysis Functions ---

def process_stock(stock_data, params):
    """Wrapper function to apply the strategy to a single stock's data."""
    stock_code, group_df = stock_data
    group_df = group_df.sort_index()
    try:
        _, trades = apply_roc_atr_strategy(group_df, **params)
        if trades:
            for trade in trades:
                trade['StockCode'] = stock_code
            return trades
    except Exception as e:
        return None
    return None

def calculate_summary_statistics(trades_df):
    """Calculates and returns a dictionary of summary statistics."""
    stats = {}
    
    # Define all possible exit reasons with default 0 values
    all_exit_reasons = ['Profit Target', 'Stop Loss', 'Max Hold Time']
    
    if trades_df.empty:
        stats["Total Trades"] = 0
        stats["Total PnL (Absolute)"] = 0
        stats["Winning Trades"] = 0
        stats["Losing Trades"] = 0
        stats["Win Rate (%)"] = 0
        stats["Average PnL per Trade (%)"] = 0.0 # Ensure this is float
        stats["Gross Profit (Absolute)"] = 0
        stats["Gross Loss (Absolute)"] = 0
        stats["Profit Factor"] = 0.0 # Default to 0.0 for no trades
        stats["Average Holding Days"] = 0
        
        stats["Exit Reason Counts"] = {reason: 0 for reason in all_exit_reasons}
        stats["Exit Reason Percentages Raw"] = {reason: 0.0 for reason in all_exit_reasons}
        return stats

    stats["Total Trades"] = len(trades_df)
    winning_trades_df = trades_df[trades_df['PnL_absolute'] > 0]
    losing_trades_df = trades_df[trades_df['PnL_absolute'] <= 0] 

    stats["Winning Trades"] = len(winning_trades_df)
    stats["Losing Trades"] = len(losing_trades_df)
    
    stats["Win Rate (%)"] = (stats["Winning Trades"] / stats["Total Trades"]) * 100 if stats["Total Trades"] > 0 else 0

    stats["Average PnL per Trade (%)"] = trades_df['PnL_pct'].mean() 

    stats["Total PnL (Absolute)"] = trades_df['PnL_absolute'].sum()
    stats["Gross Profit (Absolute)"] = winning_trades_df['PnL_absolute'].sum()
    stats["Gross Loss (Absolute)"] = abs(losing_trades_df['PnL_absolute'].sum())

    if stats["Gross Loss (Absolute)"] > 0:
        stats["Profit Factor"] = stats["Gross Profit (Absolute)"] / stats["Gross Loss (Absolute)"]
    else:
        stats["Profit Factor"] = float('inf') 
        
    stats["Average Holding Days"] = trades_df['HoldingDays'].mean()
    
    exit_reason_counts = trades_df['ExitReason'].value_counts().to_dict()
    exit_reason_pct_raw = trades_df['ExitReason'].value_counts(normalize=True).mul(100).to_dict()
    
    # Ensure all_exit_reasons are present in exit_reason_pct_raw
    stats["Exit Reason Counts"] = {reason: exit_reason_counts.get(reason, 0) for reason in all_exit_reasons}
    stats["Exit Reason Percentages Raw"] = {reason: exit_reason_pct_raw.get(reason, 0.0) for reason in all_exit_reasons}

    return stats
    stats["Total Trades"] = len(trades_df)
    winning_trades_df = trades_df[trades_df['PnL_absolute'] > 0]
    losing_trades_df = trades_df[trades_df['PnL_absolute'] <= 0]
    stats["Winning Trades"] = len(winning_trades_df)
    stats["Losing Trades"] = len(losing_trades_df)
    stats["Win Rate (%)"] = (stats["Winning Trades"] / stats["Total Trades"]) * 100 if stats["Total Trades"] > 0 else 0
    stats["Average PnL per Trade (%)"] = trades_df['PnL_pct'].mean()
    stats["Total PnL (Absolute)"] = trades_df['PnL_absolute'].sum()
    stats["Gross Profit (Absolute)"] = winning_trades_df['PnL_absolute'].sum()
    stats["Gross Loss (Absolute)"] = abs(losing_trades_df['PnL_absolute'].sum())
    stats["Profit Factor"] = stats["Gross Profit (Absolute)"] / stats["Gross Loss (Absolute)"] if stats["Gross Loss (Absolute)"] > 0 else float('inf')
    stats["Average Holding Days"] = trades_df['HoldingDays'].mean()
    stats["Exit Reason Counts"] = trades_df['ExitReason'].value_counts().to_dict()
    return stats

def run_single_backtest(params, df_all, desc_param_name, desc_param_value, start_date=None, end_date=None):
    """Runs a backtest for a given set of parameters and returns summary stats."""
    # Filter df_all by date range if provided
    if start_date:
        df_all = df_all[df_all.index >= start_date]
    if end_date:
        df_all = df_all[df_all.index <= end_date]

    grouped = df_all.groupby('StockCode')
    tasks = list(grouped)
    process_func = partial(process_stock, params=params)
    all_trades = []
    
    with Pool(cpu_count()) as p:
        results = list(tqdm(p.imap(process_func, tasks), total=len(tasks), desc=f"Testing {desc_param_name}={desc_param_value}"))
        for res in results:
            if res:
                all_trades.extend(res)

    if all_trades:
        return calculate_summary_statistics(pd.DataFrame(all_trades))
    return calculate_summary_statistics(pd.DataFrame())

def plot_sensitivity_analysis(results_df, param_name, base_params):
    """Plots the results of the sensitivity analysis."""
    setup_chinese_font()
    fig, ax1 = plt.subplots(figsize=(18, 10))

    param_title = param_name.replace("_", " ").title()
    colors = ['#4C72B0', '#55A868', '#C44E52', '#8172B2', '#CCB974', '#64B5CD', '#FF8C00']
    
    # Bar for Total Return
    ax1.bar(results_df.index.astype(str), results_df['Total PnL (Absolute)'], color='purple', alpha=0.7, label='总回报')
    ax1.set_ylabel('总回报', color='purple', fontsize=14)
    ax1.tick_params(axis='y', labelcolor='purple')
    ax1.set_xlabel(param_title, fontsize=14)

    ax2 = ax1.twinx()
    ax2.plot(results_df.index.astype(str), results_df['Total Trades'], color=colors[0], marker='o', label='总交易次数')
    ax2.set_ylabel('交易次数', color=colors[0], fontsize=14)
    ax2.tick_params(axis='y', labelcolor=colors[0])

    ax3 = ax1.twinx()
    ax3.spines['right'].set_position(('outward', 60))
    ax3.plot(results_df.index.astype(str), results_df['Win Rate (%)'], color=colors[4], linestyle='--', marker='x', label='胜率 (%)')
    ax3.plot(results_df.index.astype(str), results_df['Average PnL per Trade (%)'], color=colors[6], linestyle='-.', marker='^', label='平均单笔盈亏 (%)')
    ax3.plot(results_df.index.astype(str), results_df['Profit Target %'], color=colors[1], linestyle='-', marker='o', label='止盈次数 (%)')
    ax3.plot(results_df.index.astype(str), results_df['Stop Loss %'], color=colors[2], linestyle='-', marker='s', label='止损次数 (%)')
    ax3.plot(results_df.index.astype(str), results_df['Max Hold Time %'], color=colors[3], linestyle='-', marker='D', label='持仓到期次数 (%)')
    ax3.set_ylabel('百分比', color=colors[4], fontsize=14)
    ax3.tick_params(axis='y', labelcolor=colors[4])
    ax3.set_ylim(bottom=0)

    ax4 = ax1.twinx()
    ax4.spines['right'].set_position(('outward', 120)) # Offset from ax3
    ax4.plot(results_df.index.astype(str), results_df['Profit Factor'], color=colors[5], linestyle=':', marker='s', label='利润因子')
    ax4.set_ylabel('利润因子', color=colors[5], fontsize=14)
    ax4.tick_params(axis='y', labelcolor=colors[5])
    ax4.set_ylim(bottom=0)

    # Legends
    lines, labels = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    lines3, labels3 = ax3.get_legend_handles_labels()
    lines4, labels4 = ax4.get_legend_handles_labels()
    ax1.legend(lines + lines2 + lines3 + lines4, labels + labels2 + labels3 + labels4, loc='upper center', bbox_to_anchor=(0.5, -0.1), ncol=5)

    plt.title(f'对 {param_title} 的敏感性分析', fontsize=18, pad=20)
    plt.grid(True)
    fig.tight_layout(rect=[0, 0.05, 1, 0.95])
    
    output_dir = os.path.join(project_root, 'strategies', 'RoC', 'sensitivity_analysis_results')
    os.makedirs(output_dir, exist_ok=True)
    filename = os.path.join(output_dir, f'sensitivity_analysis_{param_name}.png')
    plt.savefig(filename)
    print(f"图表已保存至: {filename}")
    plt.show()


def main():
    base_params = {
        'roc_period': 20, 'roc_std_window': 750, 'roc_std_multiplier': 1.7,
        'atr_period': 14, 'profit_target_atr_multiplier': 2.0,
        'stop_loss_atr_multiplier': 2.5, 'max_hold_days': 40
    }
    
    param_to_test = 'max_hold_days'
    param_values = [10, 20, 30, 40, 50, 60]

    # Define In-Sample period
    IN_SAMPLE_START_DATE = '2010-01-01'
    IN_SAMPLE_END_DATE = '2023-12-31'

    print(f"--- 开始对以下参数进行敏感性分析: {param_to_test} (In-Sample: {IN_SAMPLE_START_DATE} to {IN_SAMPLE_END_DATE}) ---")
    print(f"测试值: {param_values}")

    market_data_path = os.path.join(project_root, 'ashare', 'all_daily_hfq.parquet')
    df_all = pd.read_parquet(market_data_path)
    df_all.rename(columns={'日期': 'Date', '股票代码': 'StockCode', '开盘': 'Open', '最高': 'High', '最低': 'Low', '收盘': 'Close', '成交量': 'Volume'}, inplace=True)
    df_all['Date'] = pd.to_datetime(df_all['Date'])
    df_all.set_index('Date', inplace=True)

    results = []
    for value in param_values:
        current_params = base_params.copy()
        current_params[param_to_test] = value
        summary_stats = run_single_backtest(current_params, df_all, param_to_test, value, start_date=IN_SAMPLE_START_DATE, end_date=IN_SAMPLE_END_DATE)
        summary_stats[param_to_test] = value
        results.append(summary_stats)

    results_df = pd.DataFrame(results).set_index(param_to_test)
    results_df['Max Hold Time %'] = results_df['Exit Reason Percentages Raw'].apply(lambda x: x.get('Max Hold Time', 0))
    results_df['Profit Target %'] = results_df['Exit Reason Percentages Raw'].apply(lambda x: x.get('Profit Target', 0))
    results_df['Stop Loss %'] = results_df['Exit Reason Percentages Raw'].apply(lambda x: x.get('Stop Loss', 0))
    
    print("\n--- 敏感性分析结果 ---")
    display_cols = ['Total PnL (Absolute)', 'Total Trades', 'Win Rate (%)', 'Average PnL per Trade (%)', 'Average Holding Days', 'Profit Factor', 'Profit Target %', 'Stop Loss %', 'Max Hold Time %']
    print(results_df[display_cols])

    plot_sensitivity_analysis(results_df, param_to_test, base_params)

if __name__ == "__main__":
    main() 