import pandas as pd
import os
import numpy as np
import sys

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from utilities.utils import get_project_root

def analyze_trades():
    """
    Analyzes completed trades from a CSV file to understand performance
    based on exit reasons and saves the output as a Markdown file.
    """
    # Get the directory of the current script and define output paths
    script_dir = os.path.dirname(os.path.abspath(__file__))
    output_dir = os.path.join(script_dir, "output")
    os.makedirs(output_dir, exist_ok=True)
    
    file_path = os.path.join(script_dir, "..", "backtest_results", "completed_trades_in_sample.csv")
    md_output_path = os.path.join(output_dir, "completed_trades_analysis.md")
    
    md_parts = []

    try:
        # Read the CSV file
        df = pd.read_csv(file_path)

        # Rename columns for easier access
        df.rename(columns={
            'StockCode': 'Ticker',
            'PnL %': 'Return',
            'Annualized Vol (250d)': 'Annualized Vol',
            'EntryPrice/EMA20': 'EntryPrice/EMA20',
            'RoC(5)': 'RoC5',
            'Max(5)': 'Max5',
            'Abn_turnover(5)': 'Abn_turnover5',
            'Vol(5)': 'Vol5',
            'ExitNAV': 'ExitNAV'
        }, inplace=True)

        # --- Data Cleaning ---
        numeric_cols = ['Return', 'Annualized Vol', 'RoC5', 'Max5', 'Abn_turnover5', 'Vol5', 'EntryPrice/EMA20', 'ExitNAV', 'PnL']
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df.dropna(subset=['Return', 'Annualized Vol', 'ExitNAV', 'Abn_turnover5', 'Vol5', 'EntryPrice/EMA20'], inplace=True)
        df = df[df['Return'] < 500] # Filter outliers

        # --- Analysis Functions ---
        def reward_risk_ratio(series):
            wins = series[series > 0].mean()
            losses = series[series < 0].mean()
            return (wins / abs(losses)) if pd.notna(losses) and losses != 0 and pd.notna(wins) else (np.inf if pd.notna(wins) else 0)

        def win_rate(series):
            return (series > 0).sum() / series.count() if series.count() > 0 else 0

        # --- Grouped Analysis ---
        
        # 1. By Exit Reason
        grouped_reason = df.groupby('ExitReason')
        analysis_reason = grouped_reason.agg(
            **{'Avg PnL %': ('Return', 'mean'), 'Avg Annualized Vol': ('Annualized Vol', 'mean'), 'Trade Count': ('Ticker', 'count'), 'Total PnL': ('PnL', 'sum')}
        )
        analysis_reason['Total PnL'] = analysis_reason['Total PnL'].astype(int)
        print("Comprehensive Analysis of Trades by Exit Reason:")
        print(analysis_reason.round(2))
        md_parts.append("# Completed Trades Analysis")
        md_parts.append("## Analysis by Exit Reason")
        md_parts.append(analysis_reason.round(2).to_markdown())

        # Helper for bucket analysis
        def analyze_bucket(df, column_name, bins, labels, title):
            if column_name not in df.columns:
                return
            
            bucket_col = f'{column_name} Bucket'
            df[bucket_col] = pd.cut(df[column_name], bins=bins, labels=labels, right=False)
            grouped = df.groupby(bucket_col, observed=False)
            
            result = grouped.agg(
                **{'Trade Count': ('Ticker', 'count'), 'Total PnL': ('PnL', 'sum'), 'Win Rate': ('Return', win_rate), 'Reward/Risk Ratio': ('Return', reward_risk_ratio), 'Avg PnL %': ('Return', 'mean')}
            ).reset_index()
            result['Total PnL'] = result['Total PnL'].astype(int)
            
            print(f"\n{'='*80}\n{title}:")
            print(result.round(2))
            md_parts.append(f"## {title}")
            md_parts.append(result.round(2).to_markdown(index=False))

        # 2. By Volatility
        vol_bins = [0, 0.2, 0.3, 0.4, 0.5, 0.6, np.inf]
        vol_labels = ['0-20%', '20-30%', '30-40%', '40-50%', '50-60%', '>60%']
        analyze_bucket(df, 'Annualized Vol', vol_bins, vol_labels, "Analysis by Annualized Volatility")

        # 3. By EntryPrice/EMA20 Ratio
        ema_bins = [0, 0.6, 0.7, 0.8, 0.9, 1.0, np.inf]
        ema_labels = ['<0.6', '0.6-0.7', '0.7-0.8', '0.8-0.9', '0.9-1.0', '>1.0']
        analyze_bucket(df, 'EntryPrice/EMA20', ema_bins, ema_labels, "Analysis by EntryPrice/EMA20 Ratio")

        # 4. By RoC(5)
        roc_bins = [-np.inf, -10, -5, 0, 5, 10, np.inf]
        roc_labels = ['<-10%', '-10% to -5%', '-5% to 0%', '0% to 5%', '5% to 10%', '>10%']
        analyze_bucket(df, 'RoC5', roc_bins, roc_labels, "Analysis by RoC(5)")

        # 5. By Max(5)
        max5_bins = [-np.inf, -0.02, 0, 0.02, 0.05, np.inf]
        max5_labels = ['<-2%', '-2% to 0%', '0% to 2%', '2% to 5%', '>5%']
        analyze_bucket(df, 'Max5', max5_bins, max5_labels, "Analysis by Max(5) (Max Daily Return in 5 Days)")

        # 6. By Abn_turnover(5)
        abn_bins = [0, 0.5, 1.0, 1.5, 2.0, 3.0, np.inf]
        abn_labels = ['<0.5', '0.5-1.0', '1.0-1.5', '1.5-2.0', '2.0-3.0', '>3.0']
        analyze_bucket(df, 'Abn_turnover5', abn_bins, abn_labels, "Analysis by Abnormal Turnover (5 days)")

        # 7. By Vol(5)
        vol5_bins = [0, 0.005, 0.01, 0.015, 0.02, np.inf]
        vol5_labels = ['0-0.5%', '0.5-1%', '1-1.5%', '1.5-2%', '>2%']
        analyze_bucket(df, 'Vol5', vol5_bins, vol5_labels, "Analysis by Vol(5) (Daily Return Std Dev in 5 Days)")

        # --- Yearly Comparison ---
        print(f"\n{'='*80}\nRoC Strategy - 年度策略净值回报 vs 沪深300对比：")

        try:
            # Load HS300 index data
            hs300_path = os.path.join(get_project_root(), 'ashare', 'Indices_daily', 'sh000300.csv')
            hs300 = pd.read_csv(hs300_path, usecols=['日期', '收盘'], parse_dates=['日期'])
            hs300.rename(columns={'日期': 'Date', '收盘': 'Close'}, inplace=True)
            hs300['Year'] = hs300['Date'].dt.year
            hs300_returns = hs300.groupby('Year')['Close'].last().pct_change() * 100

            # Process in-sample data (up to 2023)
            df['ExitDate'] = pd.to_datetime(df['ExitDate'], errors='coerce')
            df.sort_values('ExitDate', inplace=True)
            nav_df = df[['ExitDate', 'ExitNAV']].dropna()
            nav_df['Year'] = nav_df['ExitDate'].dt.year

            # Get in-sample yearly NAV (up to 2023)
            in_sample_nav = nav_df[nav_df['Year'] <= 2023].groupby('Year')['ExitNAV'].last()

            # Load out-of-sample NAV data for 2024-2025
            out_sample_nav_path = os.path.join(script_dir, "..", "backtest_results", "nav_history_out_of_sample.csv")
            if os.path.exists(out_sample_nav_path):
                out_nav_df = pd.read_csv(out_sample_nav_path, parse_dates=['Date'])
                out_nav_df['Year'] = out_nav_df['Date'].dt.year
                out_sample_nav = out_nav_df.groupby('Year')['NAV'].last()

                # Calculate out-of-sample returns correctly (starting from 1M)
                out_sample_returns = pd.Series(dtype=float)
                if 2024 in out_sample_nav.index:
                    # 2024 return: (end_nav - 1M) / 1M
                    out_sample_returns[2024] = (out_sample_nav[2024] - 1000000.0) / 1000000.0 * 100
                if 2025 in out_sample_nav.index:
                    # 2025 return: (end_nav - start_2025_nav) / start_2025_nav
                    start_2025_nav = out_sample_nav[2024] if 2024 in out_sample_nav.index else 1000000.0
                    out_sample_returns[2025] = (out_sample_nav[2025] - start_2025_nav) / start_2025_nav * 100

                # Calculate in-sample returns
                start_nav = pd.Series([1000000.0], index=[in_sample_nav.index.min() - 1])
                in_sample_nav_with_start = pd.concat([start_nav, in_sample_nav])
                in_sample_returns = in_sample_nav_with_start.pct_change().dropna() * 100

                # Combine returns
                strat_returns = pd.concat([in_sample_returns, out_sample_returns])
                strat_returns = strat_returns.sort_index()

                # Create comparison dataframe
                comparison_df = pd.DataFrame({
                    'RoC Strategy Return %': strat_returns.round(2),
                    'HS300 Return %': hs300_returns.round(2)
                }).reset_index().rename(columns={'index': 'Year'})
                comparison_df = comparison_df[comparison_df['Year'] >= 2010]

                # Add data source column
                comparison_df['Data Source'] = comparison_df['Year'].apply(
                    lambda x: 'In-Sample' if x <= 2023 else 'Out-of-Sample'
                )

                print("注：2024-2025年数据来自Out-of-Sample回测结果")
                print(comparison_df.to_string(index=False))
                md_parts.append("## Yearly RoC Strategy NAV Return vs HS300")
                md_parts.append("*注：2024-2025年数据来自Out-of-Sample回测结果*")
                md_parts.append(comparison_df.to_markdown(index=False))
            else:
                # Fallback to in-sample only
                yearly_nav = in_sample_nav
                start_nav = pd.Series([1000000.0], index=[yearly_nav.index.min() - 1])
                yearly_nav_with_start = pd.concat([start_nav, yearly_nav])
                strat_returns = yearly_nav_with_start.pct_change().dropna() * 100

                comparison_df = pd.DataFrame({
                    'RoC Strategy Return %': strat_returns.round(2),
                    'HS300 Return %': hs300_returns.round(2)
                }).reset_index().rename(columns={'index': 'Year'})
                comparison_df = comparison_df[comparison_df['Year'] >= 2010]

                print("警告：未找到Out-of-Sample数据，仅显示In-Sample结果")
                print(comparison_df.to_string(index=False))
                md_parts.append("## Yearly RoC Strategy NAV Return vs HS300")
                md_parts.append("*警告：未找到Out-of-Sample数据，仅显示In-Sample结果*")
                md_parts.append(comparison_df.to_markdown(index=False))

        except Exception as e:
            print(f"Warning: Could not generate yearly comparison: {e}")
            import traceback
            traceback.print_exc()

        # --- Write Markdown File ---
        with open(md_output_path, 'w', encoding='utf-8') as f:
            f.write('\n\n'.join(md_parts))
        print(f"\nMarkdown report saved to: {md_output_path}")

    except FileNotFoundError:
        print(f"Error: The file {file_path} was not found.")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    analyze_trades()