# market_regime_detector.py
# 市场状态识别模块

import pandas as pd
import numpy as np
from sklearn.mixture import GaussianMixture
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class MarketRegimeDetector:
    """市场状态识别器"""
    
    def __init__(self, lookback_window: int = 252):
        self.lookback_window = lookback_window
        self.regime_model = None
        self.scaler = StandardScaler()
        self.regime_history = []
        
    def calculate_market_features(self, price_series: pd.Series) -> pd.DataFrame:
        """计算市场特征指标"""
        features = pd.DataFrame(index=price_series.index)
        
        # 收益率特征
        returns = price_series.pct_change()
        features['returns'] = returns
        features['returns_volatility'] = returns.rolling(20).std()
        features['returns_skewness'] = returns.rolling(60).skew()
        features['returns_kurtosis'] = returns.rolling(60).kurt()
        
        # 趋势特征
        features['ma_20'] = price_series.rolling(20).mean()
        features['ma_60'] = price_series.rolling(60).mean()
        features['price_vs_ma20'] = (price_series - features['ma_20']) / features['ma_20']
        features['price_vs_ma60'] = (price_series - features['ma_60']) / features['ma_60']
        features['ma_slope_20'] = features['ma_20'].pct_change(5)
        
        # 波动率特征
        features['volatility_regime'] = returns.rolling(20).std() / returns.rolling(60).std()
        features['max_drawdown_20'] = self._calculate_rolling_max_drawdown(price_series, 20)
        
        # VIX代理指标（基于收益率分布）
        features['vix_proxy'] = returns.rolling(20).std() * np.sqrt(252) * 100
        
        return features.dropna()
    
    def _calculate_rolling_max_drawdown(self, price_series: pd.Series, window: int) -> pd.Series:
        """计算滚动最大回撤"""
        rolling_max = price_series.rolling(window).max()
        drawdown = (price_series - rolling_max) / rolling_max
        return drawdown.rolling(window).min()
    
    def fit_regime_model(self, market_features: pd.DataFrame, n_regimes: int = 3):
        """训练市场状态模型"""
        # 选择关键特征
        feature_cols = [
            'returns_volatility', 'price_vs_ma20', 'ma_slope_20', 
            'volatility_regime', 'max_drawdown_20', 'vix_proxy'
        ]
        
        X = market_features[feature_cols].dropna()
        
        # 标准化特征
        X_scaled = self.scaler.fit_transform(X)
        
        # 训练高斯混合模型
        self.regime_model = GaussianMixture(
            n_components=n_regimes, 
            covariance_type='full',
            random_state=42,
            max_iter=200
        )
        
        regime_labels = self.regime_model.fit_predict(X_scaled)
        
        # 解释状态含义
        regime_stats = self._interpret_regimes(X, regime_labels)
        
        return regime_labels, regime_stats
    
    def _interpret_regimes(self, features: pd.DataFrame, labels: np.ndarray) -> dict:
        """解释各个市场状态的含义"""
        regime_stats = {}
        
        for regime in np.unique(labels):
            mask = labels == regime
            regime_data = features[mask]
            
            stats = {
                'avg_volatility': regime_data['returns_volatility'].mean(),
                'avg_trend': regime_data['ma_slope_20'].mean(),
                'avg_drawdown': regime_data['max_drawdown_20'].mean(),
                'count': mask.sum(),
                'percentage': mask.sum() / len(labels) * 100
            }
            
            # 根据特征判断状态类型
            if stats['avg_volatility'] > 0.02 and stats['avg_drawdown'] < -0.1:
                regime_type = "熊市/危机"
            elif stats['avg_trend'] > 0.001 and stats['avg_volatility'] < 0.015:
                regime_type = "牛市/上涨"
            else:
                regime_type = "震荡/横盘"
            
            stats['regime_type'] = regime_type
            regime_stats[regime] = stats
        
        return regime_stats
    
    def predict_current_regime(self, market_features: pd.DataFrame) -> int:
        """预测当前市场状态"""
        if self.regime_model is None:
            raise ValueError("模型未训练，请先调用fit_regime_model")
        
        feature_cols = [
            'returns_volatility', 'price_vs_ma20', 'ma_slope_20', 
            'volatility_regime', 'max_drawdown_20', 'vix_proxy'
        ]
        
        latest_features = market_features[feature_cols].iloc[-1:].dropna()
        if latest_features.empty:
            return 0  # 默认状态
        
        X_scaled = self.scaler.transform(latest_features)
        regime = self.regime_model.predict(X_scaled)[0]
        
        return regime
    
    def get_regime_adjusted_parameters(self, base_params: dict, current_regime: int) -> dict:
        """根据市场状态调整策略参数"""
        adjusted_params = base_params.copy()
        
        # 根据不同市场状态调整参数
        if current_regime == 0:  # 假设0是熊市/危机状态
            # 在危机中更激进，降低入场门槛，提高止损
            adjusted_params['roc_std_multiplier'] *= 0.8  # 更容易触发
            adjusted_params['stop_loss_atr_multiplier'] *= 1.2  # 更严格止损
            adjusted_params['max_hold_days'] = min(20, adjusted_params['max_hold_days'])
            
        elif current_regime == 1:  # 假设1是牛市状态
            # 在牛市中更保守，提高入场门槛
            adjusted_params['roc_std_multiplier'] *= 1.3  # 更难触发
            adjusted_params['profit_target_atr_multiplier'] *= 1.2  # 更高止盈
            
        else:  # 震荡市
            # 保持默认参数或微调
            adjusted_params['max_hold_days'] = max(25, adjusted_params['max_hold_days'])
        
        return adjusted_params

def analyze_regime_performance(trades_df: pd.DataFrame, regime_labels: np.ndarray, 
                             regime_dates: pd.DatetimeIndex) -> pd.DataFrame:
    """分析不同市场状态下的策略表现"""
    
    # 创建状态映射
    regime_map = pd.Series(regime_labels, index=regime_dates)
    
    # 为每笔交易分配状态
    trades_df['EntryDate'] = pd.to_datetime(trades_df['EntryDate'])
    trades_df['MarketRegime'] = trades_df['EntryDate'].map(regime_map)
    
    # 按状态分组分析
    regime_performance = trades_df.groupby('MarketRegime').agg({
        'PnL_pct': ['count', 'mean', 'std'],
        'HoldingDays': 'mean',
        'ExitReason': lambda x: (x == 'Profit Target').sum() / len(x)
    }).round(4)
    
    regime_performance.columns = ['交易数量', '平均收益率', '收益率标准差', '平均持仓天数', '止盈率']
    
    return regime_performance

# 使用示例
if __name__ == "__main__":
    # 创建示例数据
    dates = pd.date_range('2020-01-01', '2023-12-31', freq='D')
    np.random.seed(42)
    
    # 模拟市场价格数据
    returns = np.random.normal(0.0005, 0.02, len(dates))
    # 添加一些市场状态变化
    returns[500:600] = np.random.normal(-0.003, 0.04, 100)  # 熊市期
    returns[1000:1200] = np.random.normal(0.002, 0.01, 200)  # 牛市期
    
    prices = pd.Series(100 * np.exp(np.cumsum(returns)), index=dates)
    
    # 初始化检测器
    detector = MarketRegimeDetector()
    
    # 计算市场特征
    features = detector.calculate_market_features(prices)
    
    # 训练模型
    regimes, stats = detector.fit_regime_model(features)
    
    print("市场状态统计:")
    for regime, stat in stats.items():
        print(f"状态 {regime} ({stat['regime_type']}): {stat['percentage']:.1f}%")
    
    # 预测当前状态
    current_regime = detector.predict_current_regime(features)
    print(f"\n当前市场状态: {current_regime}")
    
    # 参数调整示例
    base_params = {
        'roc_std_multiplier': 2.0,
        'stop_loss_atr_multiplier': 1.5,
        'profit_target_atr_multiplier': 2.0,
        'max_hold_days': 30
    }
    
    adjusted_params = detector.get_regime_adjusted_parameters(base_params, current_regime)
    print(f"\n调整后参数: {adjusted_params}")
