import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import sys
import os

# --- Path Setup ---
# The script is in strategies/RoC/analysis, so we go up three levels to get to the project root.
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from utilities.complete_font_solution import setup_chinese_font

# Setup Chinese font for matplotlib
setup_chinese_font()

# Get the directory of the current script to create an output folder inside it.
script_dir = os.path.dirname(os.path.abspath(__file__))
output_dir = os.path.join(script_dir, "output")
os.makedirs(output_dir, exist_ok=True)


# 1. Load and preprocess data
try:
    # Build the full path to the data file from the project root.
    file_path = os.path.join(project_root, "strategies", "RoC", "backtest_results", "nav_history_out_of_sample.csv")
    df = pd.read_csv(file_path)
    df['Date'] = pd.to_datetime(df['Date'])
    df.set_index('Date', inplace=True)
except FileNotFoundError:
    print(f"错误：找不到文件 {file_path}")
    exit()

# 2. Calculate position ratio
df['PositionRatio'] = df['PositionsValue'] / df['NAV']

# 3. Identify key time points and data
# Filter for data from 2024 onwards
df_2024 = df['2024-01-01':]

if df_2024.empty:
    print("错误：在2024年没有找到数据。")
    exit()

# Calculate maximum drawdown
peak_nav = df_2024['NAV'].cummax()
drawdown = (df_2024['NAV'] - peak_nav) / peak_nav
max_drawdown = drawdown.min()
peak_date = peak_nav.idxmax()
trough_date = drawdown.idxmin()
trough_nav = df_2024.loc[trough_date, 'NAV']

# Data for the market reversal day (2024-02-05)
market_reversal_date = pd.to_datetime('2024-02-05')
if market_reversal_date in df_2024.index:
    reversal_day_data = df_2024.loc[market_reversal_date]
else:
    # If the exact date is not a trading day, find the most recent previous one.
    reversal_day_data = df_2024[df_2024.index <= market_reversal_date].iloc[-1]

reversal_nav = reversal_day_data['NAV']
reversal_position_ratio = reversal_day_data['PositionRatio']
market_reversal_date = reversal_day_data.name # Update to the actual date

# 4. Output key metrics to a report file
report_path = os.path.join(output_dir, "drawdown_report.txt")
with open(report_path, "w", encoding="utf-8") as f:
    f.write("="*80 + "\n")
    f.write("                     2024年初策略表现量化分析\n")
    f.write("="*80 + "\n")
    f.write(f"净值最高点 ({peak_date.strftime('%Y-%m-%d')}): {df_2024.loc[peak_date, 'NAV']:,.2f}\n")
    f.write(f"净值最低点 ({trough_date.strftime('%Y-%m-%d')}): {trough_nav:,.2f}\n")
    f.write(f"最大回撤 (Max Drawdown): {max_drawdown:.2%}\n")
    f.write(f"回撤周期: 从 {peak_date.strftime('%Y-%m-%d')} 到 {trough_date.strftime('%Y-%m-%d')}\n")
    f.write(f"---\n")
    f.write(f"市场V型反转日 ({market_reversal_date.strftime('%Y-%m-%d')}) 分析:\n")
    f.write(f"  - 当日净值: {reversal_nav:,.2f}\n")
    f.write(f"  - 当日仓位: {reversal_position_ratio:.2%}\n")
    f.write("="*80 + "\n")

print(f"分析报告已保存至: {report_path}")


# 5. Generate analysis plot
fig, ax1 = plt.subplots(figsize=(16, 8))

# Plot NAV curve
ax1.plot(df_2024.index, df_2024['NAV'], color='#007ACC', label='策略净值 (NAV)')
ax1.set_xlabel('日期')
ax1.set_ylabel('策略净值 (NAV)', color='#007ACC')
ax1.tick_params(axis='y', labelcolor='#007ACC')
ax1.grid(True, linestyle='--', alpha=0.6)

# Mark peak and trough points
ax1.plot(peak_date, df_2024.loc[peak_date, 'NAV'], 'o', color='green', markersize=10, label=f"最高点: {df_2024.loc[peak_date, 'NAV']:,.0f}")
ax1.plot(trough_date, trough_nav, 'o', color='red', markersize=10, label=f"最低点: {trough_nav:,.0f}")

# Mark market reversal day
ax1.axvline(x=market_reversal_date, color='purple', linestyle='--', linewidth=2, label=f"市场反转日 (02-05)")

# Create a second Y-axis for position ratio
ax2 = ax1.twinx()
ax2.fill_between(df_2024.index, df_2024['PositionRatio'], color='#FFB84D', alpha=0.4, label='仓位比例')
ax2.set_ylabel('仓位比例', color='#FFB84D')
ax2.tick_params(axis='y', labelcolor='#FFB84D')
ax2.set_ylim(0, 1.05) # Position ratio is between 0 and 1

# Legend
fig.legend(loc='upper center', bbox_to_anchor=(0.5, 0.95), ncol=4)
plt.title('2024年初策略净值、仓位与市场走势分析', fontsize=18, pad=40)
fig.tight_layout(rect=[0, 0, 1, 0.9]) # Adjust layout for title and legend

# Format X-axis date
ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
plt.xticks(rotation=45)

# Save the plot to the output directory
plot_path = os.path.join(output_dir, "drawdown_analysis.png")
plt.savefig(plot_path, dpi=300, bbox_inches='tight')

print(f"分析图表已保存至: {plot_path}")