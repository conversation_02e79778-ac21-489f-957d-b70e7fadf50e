# RoC 策略说明与未来改进方向

## 策略概述

本目录包含一个基于“超跌反转”逻辑的量化策略。其核心思想是在市场非理性下跌、个股出现超跌时买入，在情绪修复、价格反弹后卖出，以期捕捉“危机Alpha”。

详细的策略表现分析参见 `strategy_performance_analysis.md`。

---

## 回测方法论更新 (Backtesting Methodology Update)

为了更严谨地评估策略的稳健性和避免过拟合，我们引入了**样本内 (In-Sample)** 和 **样本外 (Out-of-Sample)** 回测机制。

-   **样本内 (In-Sample) 期间**: 用于策略的开发、参数优化和敏感性分析。所有参数的确定和策略逻辑的调整都应基于此期间的数据。
    -   **时间范围**: 2010-01-01 至 2023-12-31

-   **样本外 (Out-of-Sample) 期间**: 用于独立验证策略在未知数据上的表现。在此期间，不应进行任何策略参数的调整或逻辑的修改，以模拟真实交易环境。
    -   **时间范围**: 2024-01-01 至今

**影响范围**:

-   `run_portfolio_backtest_v2.py`: 现在会分别运行并展示样本内和样本外的回测结果。
-   `optimize_roc_strategy.py`: 策略参数优化过程现在仅在样本内数据上进行。
-   `sensitivity_analysis.py`: 敏感性分析现在也仅在样本内数据上进行。

通过这种方式，我们可以更清晰地了解策略在历史数据上的表现，以及其在未来数据上的泛化能力.

---

## 目录结构与脚本说明 (Directory Structure and Script Explanation)

### 核心脚本 (Core Scripts)
本目录下包含以下核心脚本，它们协同工作以实现策略的完整回测流程：

-   `roc_strategy.py`: 核心策略逻辑实现，定义了基于RoC和ATR的买卖信号生成规则。

-   `run_full_market_backtest_RoC.py`: 用于在整个市场数据上运行策略，生成详细的交易信号（买入/卖出记录）。这是后续投资组合回测的基础数据来源。

-   `run_portfolio_backtest_RoC.py`: 基于 `run_full_market_backtest_RoC.py` 生成的交易信号，进行投资组合级别的回测。它模拟了资金管理、仓位控制、交易成本等因素，并提供了详细的净值曲线、交易日志和性能指标（包括样本内和样本外表现）。

-   `strategy_performance_analysis.md`: 详细的策略表现分析文档，通常包含图表和数据。

### 子目录结构 (Subdirectories)

-   `analysis/`: 策略分析和优化脚本
    -   `optimize_roc_strategy.py`: 策略参数优化脚本
    -   `sensitivity_analysis.py`: 敏感性分析脚本
    -   `portfolio_sensitivity_analysis.py`: 投资组合敏感性分析
    -   `market_regime_detector.py`: 市场状态检测器
    -   `balanced_optimization.py`: 平衡优化脚本
    -   `performance_comparison.py`: 性能比较工具

-   `utilities/`: 实用工具脚本
    -   `find_potential_trades.py`: 潜在交易发现工具
    -   `plot_relative_strength.py`: 相对强度绘图工具
    -   `analyze_loss_trades.py`: 亏损交易分析
    -   `run_single_stock_backtest.py`: 单股票回测工具
    -   等等...

-   `testing/`: 测试和调试脚本
    -   `debug_signal_generation.py`: 信号生成调试
    -   `test_memory_optimization.py`: 内存优化测试
    -   等等...

-   `experimental/`: 实验性功能
    -   `optimized_portfolio_backtest.py`: 优化的投资组合回测
    -   `optimized_backtest_example.py`: 优化回测示例
    -   等等...

-   `docs/`: 文档和指南
    -   `MEMORY_OPTIMIZATION_GUIDE.md`: 内存优化指南
    -   `PERFORMANCE_OPTIMIZATION_GUIDE.md`: 性能优化指南

-   `backtest_results/`: 回测结果文件
-   `sensitivity_analysis_results/`: 敏感性分析结果

### 通用工具模块 (Shared Utilities)
性能优化相关的工具已移至项目根目录的 `utilities/` 目录下，以便其他策略也能使用：
-   `utilities/performance_optimizations.py`: 内存优化、性能分析等通用工具

---

## 未来改进方向 (To-Do)

### 1. 业绩归因分析 (Performance Attribution Analysis)

**目标:**
目前策略的仓位会随市场波动进行调整（市场大跌时加仓，大涨时减仓），这本身是一种择时行为。为了更精确地度量策略的Alpha来源，需要将策略的总回报率拆分为**仓位选择（市场择时）**和**个股选择**两部分。

**建议方案:**
采用经典的**布林森模型 (Brinson Model)** 来进行业绩归因分析。该模型可以将策略超越基准的收益分解为三个部分：

1.  **资产配置收益 (Asset Allocation)**: 即“仓位导致的收益”。衡量通过动态调整股票和现金的比例带来的收益。
2.  **个股选择收益 (Stock Selection)**: 即“选股导致的收益”。衡量在给定仓位下，所选股票组合相对于市场基准的超额表现。
3.  **交互收益 (Interaction)**: 两者交叉产生的收益，通常较小。

**实施步骤:**

- [ ] **修改核心回测函数**: 调整 `utilities/utils_portfolio.py` 中的 `run_portfolio_simulation` 函数，使其能够输出每日的**持仓组合收益率**和**仓位比例**的详细日志。
- [ ] **创建新的归因分析脚本**: 新建 `strategies/RoC/performance_attribution.py` 脚本，用于加载回测日志和基准数据，并执行布林森模型的计算。
- [ ] **结果可视化**: 在新脚本中，将分析结果通过图表（如堆积柱状图）进行可视化，清晰展示每个周期内不同收益来源的贡献，并输出详细的数据到CSV文件.

### 2. 根据市场Regime来自动调整模型参数

1.  将市场分成不同的regime，牛市、熊市、波动市
2.  根据不同市场的regine来调整模型的参数


# 🔧 如何自己修改权重分配逻辑
📍 修改位置
文件：strategies/RoC/run_portfolio_backtest_RoC.py
函数：calculate_combined_position_weight (大约在第755-780行)
📝 修改步骤
1. 找到权重计算函数
`def calculate_combined_position_weight(row):
    weight = 1.0  # 基础权重`
    
2. 修改权重分配规则
# 当前的权重分配逻辑
`if row['Vol(5)'] >= vol_q9_threshold and row['RoC(5)'] <= roc_q1_threshold:
`    weight = 3.0  # 顶级信号
`elif row['Vol(5)'] >= vol_q8_threshold and row['RoC(5)'] <= roc_q2_threshold:
`    weight = 2.0  # 优秀信号
`elif row['Vol(5)'] >= vol_q7_threshold and row['RoC(5)'] <= roc_q3_threshold:
`    weight = 1.5  # 良好信号
`else:
`    weight = 1.0  # 基础信号
3. 常见修改示例
A. 修改权重倍数
`# 更保守的权重
`if row['Vol(5)'] >= vol_q9_threshold and row['RoC(5)'] <= roc_q1_threshold:
    weight = 2.5  # 改为2.5倍而不是3.0倍
B. 修改条件逻辑
# 改为OR逻辑
if row['Vol(5)'] >= vol_q9_threshold or row['RoC(5)'] <= roc_q1_threshold:
    weight = 3.0
C. 修改分位数阈值
# 使用更严格的Q95而不是Q9
if row['Vol(5)'] >= vol_q95_threshold and row['RoC(5)'] <= roc_q1_threshold:
    weight = 3.0
D. 添加新的权重层级
# 添加超级顶级信号
if row['Vol(5)'] >= vol_q95_threshold and row['RoC(5)'] <= roc_q05_threshold:
    weight = 4.0  # 超级顶级
elif row['Vol(5)'] >= vol_q9_threshold and row['RoC(5)'] <= roc_q1_threshold:
    weight = 3.0  # 顶级
4. 修改阈值计算
如果要使用新的分位数，需要在函数前面添加：
# 在calculate_combined_position_weight函数之前添加
vol_q95_threshold = trades_df_clean['Vol(5)'].quantile(0.95)
roc_q05_threshold = trades_df_clean['RoC(5)'].quantile(0.05)
5. 修改显示信息
同时记得修改权重分配规则的显示（第749-753行）：
print(f"权重分配规则 (您的自定义逻辑):")
print(f"  顶级 (3.0x): 您的条件描述")
print(f"  优秀 (2.0x): 您的条件描述")
# ... 其他规则
🚀 测试修改
修改完成后，运行以下命令测试：
FILTER_MODE=3 python strategies/RoC/run_portfolio_backtest_RoC.py
💡 修改建议
小步调整: 每次只修改一个参数，观察效果
保存备份: 修改前备份原始代码
记录变化: 记录每次修改的效果，便于对比
合理范围: 权重建议在0.5x-3.0x之间，避免过度极端
现在您知道如何自己修改权重分配逻辑了！这个系统非常灵活，您可以根据市场情况和策略需求随时调整。