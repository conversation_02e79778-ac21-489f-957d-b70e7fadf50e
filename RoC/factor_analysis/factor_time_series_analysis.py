#!/usr/bin/env python3
"""
因子时间序列稳定性分析
验证因子在不同时间段的表现稳定性
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """加载交易数据"""
    trades_path = 'strategies/RoC/backtest_results/full_market_trades_RoC.csv'
    trades_df = pd.read_csv(trades_path)
    trades_df['EntryDate'] = pd.to_datetime(trades_df['EntryDate'])
    trades_df['EntryYear'] = trades_df['EntryDate'].dt.year
    trades_df['IsWinning'] = trades_df['PnL_pct'] > 0
    return trades_df

def analyze_factor_stability_by_year(trades_df):
    """按年度分析因子稳定性"""
    print("📅 因子年度稳定性分析")
    print("="*60)
    
    factor_cols = ['RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']
    
    # 按年度分组分析
    yearly_stats = []
    
    for year in sorted(trades_df['EntryYear'].unique()):
        year_data = trades_df[trades_df['EntryYear'] == year]
        
        if len(year_data) < 50:  # 跳过交易数太少的年份
            continue
            
        year_stat = {'Year': year, 'TradeCount': len(year_data)}
        
        # 计算每个因子与收益的相关性
        for factor in factor_cols:
            if factor in year_data.columns:
                correlation = year_data[factor].corr(year_data['PnL_pct'])
                year_stat[f'{factor}_corr'] = correlation
                
                # 计算因子分位数表现
                try:
                    year_data[f'{factor}_quintile'] = pd.qcut(year_data[factor], 5, labels=False, duplicates='drop') + 1
                    quintile_performance = year_data.groupby(f'{factor}_quintile')['PnL_pct'].mean()
                    
                    # 计算Q5 vs Q1的表现差异
                    if len(quintile_performance) >= 5:
                        q5_q1_diff = quintile_performance.iloc[-1] - quintile_performance.iloc[0]
                        year_stat[f'{factor}_Q5_Q1_diff'] = q5_q1_diff
                except:
                    year_stat[f'{factor}_Q5_Q1_diff'] = np.nan
        
        # 整体表现
        year_stat['AvgReturn'] = year_data['PnL_pct'].mean()
        year_stat['WinRate'] = year_data['IsWinning'].mean()
        
        yearly_stats.append(year_stat)
    
    yearly_df = pd.DataFrame(yearly_stats)
    
    # 显示年度统计
    print(f"\n年度因子相关性统计 (共{len(yearly_df)}年):")
    correlation_cols = [col for col in yearly_df.columns if '_corr' in col]
    if correlation_cols:
        print(yearly_df[['Year'] + correlation_cols].round(3))
    
    return yearly_df

def create_stability_visualizations(yearly_df):
    """创建稳定性可视化图表"""
    print("\n📊 生成稳定性图表...")
    
    output_dir = 'strategies/RoC/factor_analysis'
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 因子相关性时间序列图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('因子与收益相关性时间序列', fontsize=16, fontweight='bold')
    
    factor_names = ['RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']
    
    for i, factor in enumerate(factor_names):
        ax = axes[i//2, i%2]
        corr_col = f'{factor}_corr'
        
        if corr_col in yearly_df.columns:
            # 绘制相关性时间序列
            ax.plot(yearly_df['Year'], yearly_df[corr_col], 'o-', linewidth=2, markersize=6)
            ax.axhline(y=0, color='red', linestyle='--', alpha=0.5)
            ax.set_title(f'{factor} 相关性')
            ax.set_xlabel('年份')
            ax.set_ylabel('与收益相关性')
            ax.grid(True, alpha=0.3)
            
            # 添加趋势线
            if len(yearly_df) > 3:
                z = np.polyfit(yearly_df['Year'], yearly_df[corr_col].fillna(0), 1)
                p = np.poly1d(z)
                ax.plot(yearly_df['Year'], p(yearly_df['Year']), "r--", alpha=0.8, linewidth=1)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/factor_stability_correlation.png', dpi=300, bbox_inches='tight')
    print(f"✅ 因子稳定性图保存至: {output_dir}/factor_stability_correlation.png")
    plt.close()
    
    # 2. 年度表现概览
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 年度平均收益
    ax1.bar(yearly_df['Year'], yearly_df['AvgReturn'], alpha=0.7, color='skyblue')
    ax1.set_title('年度平均收益')
    ax1.set_xlabel('年份')
    ax1.set_ylabel('平均收益 (%)')
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(axis='x', rotation=45)
    
    # 年度胜率
    ax2.bar(yearly_df['Year'], yearly_df['WinRate']*100, alpha=0.7, color='lightgreen')
    ax2.set_title('年度胜率')
    ax2.set_xlabel('年份')
    ax2.set_ylabel('胜率 (%)')
    ax2.grid(True, alpha=0.3)
    ax2.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/yearly_performance_overview.png', dpi=300, bbox_inches='tight')
    print(f"✅ 年度表现图保存至: {output_dir}/yearly_performance_overview.png")
    plt.close()

def analyze_market_regime_performance(trades_df):
    """分析不同市场环境下的因子表现"""
    print("\n🏛️ 市场环境分析")
    print("="*60)
    
    # 定义市场环境（基于年度收益）
    yearly_performance = trades_df.groupby('EntryYear')['PnL_pct'].mean()
    
    # 将年份分为牛市、熊市、震荡市
    performance_quantiles = yearly_performance.quantile([0.33, 0.67])
    
    def classify_market(year):
        perf = yearly_performance.get(year, 0)
        if perf > performance_quantiles.iloc[1]:
            return '牛市'
        elif perf < performance_quantiles.iloc[0]:
            return '熊市'
        else:
            return '震荡市'
    
    trades_df['MarketRegime'] = trades_df['EntryYear'].apply(classify_market)
    
    # 分析各市场环境下的因子表现
    factor_cols = ['RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']
    
    print("\n各市场环境下的因子相关性:")
    for regime in ['牛市', '震荡市', '熊市']:
        regime_data = trades_df[trades_df['MarketRegime'] == regime]
        if len(regime_data) > 100:
            print(f"\n{regime} (共{len(regime_data)}笔交易):")
            for factor in factor_cols:
                if factor in regime_data.columns:
                    correlation = regime_data[factor].corr(regime_data['PnL_pct'])
                    print(f"  {factor}: {correlation:.3f}")

def generate_factor_summary_table(trades_df):
    """生成因子总结表"""
    print("\n📋 因子总结表")
    print("="*60)
    
    factor_cols = ['RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']
    
    summary_data = []
    
    for factor in factor_cols:
        if factor not in trades_df.columns:
            continue
            
        # 基础统计
        factor_data = trades_df[factor].dropna()
        
        # 相关性
        correlation = trades_df[factor].corr(trades_df['PnL_pct'])
        
        # 分位数分析
        trades_df[f'{factor}_decile'] = pd.qcut(trades_df[factor], 10, labels=False, duplicates='drop') + 1
        decile_performance = trades_df.groupby(f'{factor}_decile')['PnL_pct'].mean()
        
        # 最优分位数
        best_decile = decile_performance.idxmax()
        best_performance = decile_performance.max()
        
        # 最差分位数
        worst_decile = decile_performance.idxmin()
        worst_performance = decile_performance.min()
        
        summary_data.append({
            '因子': factor,
            '与收益相关性': f"{correlation:.3f}",
            '最优分位数': f"Q{best_decile}",
            '最优表现': f"{best_performance:.2f}%",
            '最差分位数': f"Q{worst_decile}",
            '最差表现': f"{worst_performance:.2f}%",
            '表现差异': f"{best_performance - worst_performance:.2f}%"
        })
    
    summary_df = pd.DataFrame(summary_data)
    print(summary_df.to_string(index=False))
    
    # 保存到CSV
    output_dir = 'strategies/RoC/factor_analysis'
    os.makedirs(output_dir, exist_ok=True)
    summary_df.to_csv(f'{output_dir}/factor_summary_table.csv', index=False, encoding='utf-8-sig')
    print(f"\n✅ 因子总结表保存至: {output_dir}/factor_summary_table.csv")

def main():
    """主函数"""
    print("🕐 开始因子时间序列稳定性分析")
    print("="*60)
    
    # 加载数据
    trades_df = load_data()
    print(f"✅ 加载了 {len(trades_df)} 条交易记录")
    
    # 年度稳定性分析
    yearly_df = analyze_factor_stability_by_year(trades_df)
    
    # 创建可视化
    if len(yearly_df) > 0:
        create_stability_visualizations(yearly_df)
    
    # 市场环境分析
    analyze_market_regime_performance(trades_df)
    
    # 生成总结表
    generate_factor_summary_table(trades_df)
    
    print("\n" + "="*60)
    print("✅ 时间序列分析完成！")
    print("="*60)

if __name__ == "__main__":
    main()
