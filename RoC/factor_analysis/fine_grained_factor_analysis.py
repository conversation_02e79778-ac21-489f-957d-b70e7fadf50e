#!/usr/bin/env python3
"""
RoC策略细颗粒度因子分析
借鉴RoC_mod的多层次颗粒度分析方法
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import spearmanr, pearsonr
from itertools import combinations
import os
import warnings
from typing import Dict, Any, List, Tuple
import pickle

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_fine_grained_buckets(data: pd.Series, n_buckets: int = 15, method: str = 'quantile') -> Tuple[pd.Series, List[str]]:
    """
    创建细颗粒度的分组
    
    Args:
        data: 要分组的数据
        n_buckets: 分组数量
        method: 分组方法 ('quantile', 'equal_width', 'adaptive')
    
    Returns:
        分组标签和分组名称列表
    """
    if method == 'quantile':
        # 基于分位数的分组
        quantiles = np.linspace(0, 1, n_buckets + 1)
        bins = data.quantile(quantiles).values
        # 处理重复值
        bins = np.unique(bins)
        if len(bins) < 3:
            bins = np.linspace(data.min(), data.max(), n_buckets + 1)
        
        labels = [f'Q{i+1}' for i in range(len(bins)-1)]
        
    elif method == 'equal_width':
        # 等宽分组
        bins = np.linspace(data.min(), data.max(), n_buckets + 1)
        labels = [f'B{i+1}' for i in range(n_buckets)]
        
    elif method == 'adaptive':
        # 自适应分组：在数据密集区域创建更多分组
        from scipy.stats import gaussian_kde
        kde = gaussian_kde(data.dropna())
        x_range = np.linspace(data.min(), data.max(), 1000)
        density = kde(x_range)
        
        # 基于密度创建分组
        cumulative_density = np.cumsum(density)
        cumulative_density = cumulative_density / cumulative_density[-1]
        
        # 找到等密度分位点
        target_points = np.linspace(0, 1, n_buckets + 1)
        bin_indices = np.searchsorted(cumulative_density, target_points)
        bins = x_range[bin_indices]
        bins[0] = data.min()
        bins[-1] = data.max()
        
        labels = [f'A{i+1}' for i in range(len(bins)-1)]
    
    # 创建分组
    bucket_series = pd.cut(data, bins=bins, labels=labels, include_lowest=True, duplicates='drop')
    
    return bucket_series, labels

def calculate_smile_metrics(bucket_stats: pd.DataFrame) -> Dict[str, float]:
    """
    计算微笑曲线的各种指标
    """
    returns = bucket_stats['mean'].values
    positions = np.arange(len(returns))
    
    # 1. 微笑强度：最高收益率 - 最低收益率
    smile_strength = returns.max() - returns.min()
    
    # 2. 距离相关性：与位置的相关性
    distance_corr = abs(spearmanr(positions, returns)[0]) if len(returns) > 2 else 0
    
    # 3. U型系数：二次项系数
    if len(returns) >= 3:
        try:
            coeffs = np.polyfit(positions, returns, 2)
            u_shape_coeff = coeffs[0]  # 二次项系数
        except:
            u_shape_coeff = 0
    else:
        u_shape_coeff = 0
    
    # 4. 统计显著性
    try:
        if len(returns) > 2 and len(bucket_stats) > 1:
            # 准备数据进行Kruskal-Wallis检验
            groups = []
            for idx in bucket_stats.index:
                if 'values' in bucket_stats.columns and len(bucket_stats.loc[idx, 'values']) > 0:
                    groups.append(bucket_stats.loc[idx, 'values'])

            if len(groups) >= 2 and all(len(group) > 0 for group in groups):
                _, p_value = stats.kruskal(*groups)
            else:
                p_value = 1.0
        else:
            p_value = 1.0
    except Exception as e:
        print(f"统计检验出错: {e}")
        p_value = 1.0
    
    return {
        'smile_strength': smile_strength,
        'distance_correlation': distance_corr,
        'u_shape_coefficient': u_shape_coeff,
        'p_value': p_value
    }

def analyze_smile_curve_detailed(df: pd.DataFrame, indicator: str, indicator_name: str) -> Dict[str, Any]:
    """
    对单个指标进行详细的微笑曲线分析
    """
    results = {}
    
    # 1. 多种颗粒度分析
    granularities = {
        'coarse': 5,
        'medium': 10, 
        'fine': 15,
        'ultra_fine': 20
    }
    
    for granularity, n_buckets in granularities.items():
        try:
            # 分位数分组
            buckets, labels = create_fine_grained_buckets(df[indicator], n_buckets, 'quantile')
            
            # 计算每个分组的统计信息
            bucket_stats = df.groupby(buckets, observed=False)['PnL_pct'].agg([
                'count', 'mean', 'std', 'median',
                lambda x: np.percentile(x, 25),
                lambda x: np.percentile(x, 75),
                lambda x: list(x)  # 保存原始值用于统计检验
            ]).rename(columns={'<lambda_0>': 'q25', '<lambda_1>': 'q75', '<lambda_2>': 'values'})
            
            # 过滤样本数量太少的分组
            bucket_stats = bucket_stats[bucket_stats['count'] >= 10]
            
            if len(bucket_stats) >= 3:
                # 计算微笑曲线指标
                smile_metrics = calculate_smile_metrics(bucket_stats)
                
                results[granularity] = {
                    'n_buckets': len(bucket_stats),
                    'bucket_stats': bucket_stats,
                    **smile_metrics
                }
            
        except Exception as e:
            print(f"分析 {indicator} 的 {granularity} 颗粒度时出错: {e}")
            continue
    
    return results

def create_ultra_fine_2d_analysis(df: pd.DataFrame, ind1: str, ind2: str, n_bins: int = 10) -> Dict[str, Any]:
    """
    创建超细颗粒度的2D交互效应分析
    """
    # 使用分位数创建更细的分组
    bins1 = df[ind1].quantile(np.linspace(0, 1, n_bins + 1)).values
    bins2 = df[ind2].quantile(np.linspace(0, 1, n_bins + 1)).values
    
    # 处理重复值
    bins1 = np.unique(bins1)
    bins2 = np.unique(bins2)
    
    if len(bins1) < 3 or len(bins2) < 3:
        return None
    
    # 创建分组标签，简化Abn_turnover(5)为ABN(5)
    def simplify_label(indicator):
        if 'Abn_turnover(5)' in indicator:
            return indicator.replace('Abn_turnover(5)', 'ABN(5)')
        return indicator

    labels1 = [f'{simplify_label(ind1)}_Q{i+1}' for i in range(len(bins1)-1)]
    labels2 = [f'{simplify_label(ind2)}_Q{i+1}' for i in range(len(bins2)-1)]
    
    # 分组
    group1 = pd.cut(df[ind1], bins=bins1, labels=labels1, include_lowest=True, duplicates='drop')
    group2 = pd.cut(df[ind2], bins=bins2, labels=labels2, include_lowest=True, duplicates='drop')
    
    # 创建2D矩阵
    interaction_stats = df.groupby([group1, group2], observed=False)['PnL_pct'].agg([
        'count', 'mean', 'std', 'median',
        lambda x: np.percentile(x, 25),
        lambda x: np.percentile(x, 75),
        lambda x: (x > 0).sum() / len(x) if len(x) > 0 else 0  # 胜率
    ]).rename(columns={'<lambda_0>': 'q25', '<lambda_1>': 'q75', '<lambda_2>': 'win_rate'})
    
    # 过滤样本数量太少的组合
    valid_combinations = interaction_stats[interaction_stats['count'] >= 10]
    
    if len(valid_combinations) == 0:
        return None
    
    # 找到最佳和最差组合
    best_idx = valid_combinations['mean'].idxmax()
    worst_idx = valid_combinations['mean'].idxmin()
    
    best_return = valid_combinations.loc[best_idx, 'mean']
    worst_return = valid_combinations.loc[worst_idx, 'mean']
    
    # 创建矩阵用于热力图
    mean_matrix = interaction_stats['mean'].unstack(fill_value=np.nan)
    count_matrix = interaction_stats['count'].unstack(fill_value=0)
    win_rate_matrix = interaction_stats['win_rate'].unstack(fill_value=np.nan)  # 添加胜率矩阵

    # 计算交互强度
    interaction_strength = (best_return - worst_return) / abs(worst_return) if worst_return != 0 else 0

    return {
        'best_combination': best_idx,
        'worst_combination': worst_idx,
        'best_return': best_return,
        'worst_return': worst_return,
        'return_range': best_return - worst_return,
        'interaction_strength': interaction_strength,
        'valid_combinations': valid_combinations,
        'mean_matrix': mean_matrix,
        'count_matrix': count_matrix,
        'win_rate_matrix': win_rate_matrix,  # 添加胜率矩阵到返回值
        'n_valid_combinations': len(valid_combinations)
    }

def analyze_three_dimensional_interactions(df: pd.DataFrame, indicators: List[str]) -> Dict[str, Any]:
    """
    分析三维交互效应
    """
    three_d_results = {}
    
    for ind1, ind2, ind3 in combinations(indicators, 3):
        if not all(ind in df.columns for ind in [ind1, ind2, ind3]):
            continue
        
        # 为每个指标创建3分组（低、中、高）
        n_bins = 3
        
        bins1 = df[ind1].quantile([0, 0.33, 0.67, 1.0]).values
        bins2 = df[ind2].quantile([0, 0.33, 0.67, 1.0]).values
        bins3 = df[ind3].quantile([0, 0.33, 0.67, 1.0]).values
        
        # 处理重复值
        bins1 = np.unique(bins1)
        bins2 = np.unique(bins2)
        bins3 = np.unique(bins3)
        
        if len(bins1) < 3 or len(bins2) < 3 or len(bins3) < 3:
            continue
        
        # 创建分组标签
        labels1 = ['Low', 'Medium', 'High'][:len(bins1)-1]
        labels2 = ['Low', 'Medium', 'High'][:len(bins2)-1]
        labels3 = ['Low', 'Medium', 'High'][:len(bins3)-1]
        
        # 分组
        group1 = pd.cut(df[ind1], bins=bins1, labels=labels1, include_lowest=True, duplicates='drop')
        group2 = pd.cut(df[ind2], bins=bins2, labels=labels2, include_lowest=True, duplicates='drop')
        group3 = pd.cut(df[ind3], bins=bins3, labels=labels3, include_lowest=True, duplicates='drop')
        
        # 创建3D分析
        three_d_stats = df.groupby([group1, group2, group3], observed=False)['PnL_pct'].agg([
            'count', 'mean', 'std'
        ])
        
        # 过滤样本数量太少的组合
        valid_combinations = three_d_stats[three_d_stats['count'] >= 20]
        
        if len(valid_combinations) > 0:
            best_idx = valid_combinations['mean'].idxmax()
            best_return = valid_combinations.loc[best_idx, 'mean']
            worst_return = valid_combinations['mean'].min()
            
            three_d_results[f'{ind1}_{ind2}_{ind3}'] = {
                'best_combination': best_idx,
                'best_return': best_return,
                'worst_return': worst_return,
                'return_range': best_return - worst_return,
                'n_valid_combinations': len(valid_combinations),
                'valid_combinations': valid_combinations
            }
    
    return three_d_results

def create_interaction_heatmaps(ultra_fine_2d_results: Dict[str, Any], output_dir: str):
    """
    创建交互效应热力图，包含边际效应
    """
    try:
        for pair, data in ultra_fine_2d_results.items():
            if data is None:
                continue

            # 创建子图
            fig, axes = plt.subplots(1, 3, figsize=(20, 7))
            fig.suptitle(f'{pair.replace("_vs_", " × ")} - 交互效应分析', fontsize=16, fontweight='bold')

            # 获取原始矩阵
            mean_matrix = data['mean_matrix']
            count_matrix = data['count_matrix']
            win_rate_matrix = data.get('win_rate_matrix', None)

            # 1. 平均收益率热力图（带边际平均）
            masked_mean = mean_matrix.copy()
            masked_mean[count_matrix < 10] = np.nan

            # 计算边际平均（忽略NaN值）
            row_means = masked_mean.mean(axis=1, skipna=True)  # 每行平均
            col_means = masked_mean.mean(axis=0, skipna=True)  # 每列平均

            # 创建扩展矩阵（添加边际统计）
            extended_mean = pd.DataFrame(index=list(masked_mean.index) + ['列平均'],
                                       columns=list(masked_mean.columns) + ['行平均'], dtype=float)
            extended_mean.iloc[:-1, :-1] = masked_mean.astype(float)
            extended_mean.iloc[:-1, -1] = row_means.astype(float)  # 行平均
            extended_mean.iloc[-1, :-1] = col_means.astype(float)  # 列平均
            extended_mean.iloc[-1, -1] = float(masked_mean.mean().mean())  # 总平均

            sns.heatmap(extended_mean, annot=True, fmt='.2f', cmap='RdYlBu_r',
                       center=0, ax=axes[0], cbar_kws={'label': '平均收益率 (%)'})
            axes[0].set_title('平均收益率热力图 (含边际平均)')

            # 2. 样本数量热力图（不含边际加总）
            # 直接使用原始count_matrix，不添加边际统计
            count_matrix_clean = count_matrix.fillna(0).astype(int)

            # 创建热力图，确保数值显示
            im = axes[1].imshow(count_matrix_clean.values, cmap='Blues', aspect='auto')

            # 添加颜色条
            cbar = plt.colorbar(im, ax=axes[1])
            cbar.set_label('样本数量')

            # 添加数值注释
            for i in range(len(count_matrix_clean)):
                for j in range(len(count_matrix_clean.columns)):
                    value = count_matrix_clean.iloc[i, j]
                    axes[1].text(j, i, str(int(value)), ha='center', va='center',
                               color='white' if value > count_matrix_clean.values.max()/2 else 'black',
                               fontweight='bold')

            # 设置坐标轴
            axes[1].set_xticks(range(len(count_matrix_clean.columns)))
            axes[1].set_yticks(range(len(count_matrix_clean.index)))
            axes[1].set_xticklabels(count_matrix_clean.columns, rotation=45, ha='right')
            axes[1].set_yticklabels(count_matrix_clean.index)
            axes[1].set_title('样本数量分布')

            # 3. 胜率热力图（带边际平均）
            if win_rate_matrix is not None and not win_rate_matrix.empty:
                masked_winrate = win_rate_matrix.copy()
                masked_winrate[count_matrix < 10] = np.nan

                # 计算胜率的边际平均（加权平均，权重为样本数量）
                # 行加权平均
                row_weighted_means = []
                for i in range(len(masked_winrate)):
                    row_data = masked_winrate.iloc[i]
                    row_counts = count_matrix.iloc[i]
                    valid_mask = ~row_data.isna() & (row_counts >= 10)
                    if valid_mask.sum() > 0:
                        weighted_mean = (row_data[valid_mask] * row_counts[valid_mask]).sum() / row_counts[valid_mask].sum()
                        row_weighted_means.append(weighted_mean)
                    else:
                        row_weighted_means.append(np.nan)

                # 列加权平均
                col_weighted_means = []
                for j in range(len(masked_winrate.columns)):
                    col_data = masked_winrate.iloc[:, j]
                    col_counts = count_matrix.iloc[:, j]
                    valid_mask = ~col_data.isna() & (col_counts >= 10)
                    if valid_mask.sum() > 0:
                        weighted_mean = (col_data[valid_mask] * col_counts[valid_mask]).sum() / col_counts[valid_mask].sum()
                        col_weighted_means.append(weighted_mean)
                    else:
                        col_weighted_means.append(np.nan)

                # 总加权平均
                valid_mask = ~masked_winrate.isna() & (count_matrix >= 10)
                if valid_mask.sum().sum() > 0:
                    total_weighted_mean = (masked_winrate[valid_mask] * count_matrix[valid_mask]).sum().sum() / count_matrix[valid_mask].sum().sum()
                else:
                    total_weighted_mean = np.nan

                # 创建扩展矩阵
                extended_winrate = pd.DataFrame(index=list(masked_winrate.index) + ['列平均'],
                                              columns=list(masked_winrate.columns) + ['行平均'], dtype=float)
                extended_winrate.iloc[:-1, :-1] = (masked_winrate * 100).astype(float)  # 转换为百分比
                extended_winrate.iloc[:-1, -1] = [float(x*100) if not pd.isna(x) else np.nan for x in row_weighted_means]
                extended_winrate.iloc[-1, :-1] = [float(x*100) if not pd.isna(x) else np.nan for x in col_weighted_means]
                extended_winrate.iloc[-1, -1] = float(total_weighted_mean * 100) if not pd.isna(total_weighted_mean) else np.nan

                sns.heatmap(extended_winrate, annot=True, fmt='.1f', cmap='RdYlGn',
                           ax=axes[2], cbar_kws={'label': '胜率 (%)'})
                axes[2].set_title('胜率热力图 (含边际加权平均)')
            else:
                # 如果没有胜率数据，显示一个空白图表并添加说明
                axes[2].text(0.5, 0.5, '胜率数据不可用', ha='center', va='center',
                           transform=axes[2].transAxes, fontsize=14)
                axes[2].set_title('胜率热力图 (数据不可用)')
                axes[2].set_xticks([])
                axes[2].set_yticks([])

            plt.tight_layout()
            plt.savefig(f'{output_dir}/{pair}_interaction_heatmap.png', dpi=300, bbox_inches='tight')
            plt.close()

    except Exception as e:
        print(f"创建热力图时出错: {e}")

def create_multi_granularity_plots(results: Dict[str, Any], output_dir: str):
    """
    创建多颗粒度分析图表（箱线图样式）
    """
    for indicator_name, indicator_results in results.items():
        if not indicator_results:
            continue

        fig, axes = plt.subplots(2, 2, figsize=(16, 14))
        fig.suptitle(f'{indicator_name} - 多颗粒度微笑曲线分析（箱线图样式）', fontsize=16, fontweight='bold')

        granularities = ['coarse', 'medium', 'fine', 'ultra_fine']
        colors = ['blue', 'green', 'orange', 'red']

        for i, (granularity, color) in enumerate(zip(granularities, colors)):
            if granularity not in indicator_results:
                continue

            ax = axes[i//2, i%2]
            bucket_stats = indicator_results[granularity]['bucket_stats']

            x_pos = range(len(bucket_stats))

            # 获取统计数据
            medians = bucket_stats['median'].values
            q25s = bucket_stats['q25'].values
            q75s = bucket_stats['q75'].values
            means = bucket_stats['mean'].values

            # 计算5%和95%分位数（过滤极端值）
            mins = []  # 5%分位数
            maxs = []  # 95%分位数
            for idx in bucket_stats.index:
                if 'values' in bucket_stats.columns and len(bucket_stats.loc[idx, 'values']) > 0:
                    values = bucket_stats.loc[idx, 'values']
                    mins.append(np.percentile(values, 5))   # 5%分位数
                    maxs.append(np.percentile(values, 95))  # 95%分位数
                else:
                    # 如果没有原始值，使用均值±1.5倍标准差作为估计
                    mean_val = bucket_stats.loc[idx, 'mean']
                    std_val = bucket_stats.loc[idx, 'std']
                    mins.append(mean_val - 1.5*std_val)
                    maxs.append(mean_val + 1.5*std_val)

            mins = np.array(mins)
            maxs = np.array(maxs)

            # 绘制箱线图样式的图表
            box_width = 0.6

            for j, x in enumerate(x_pos):
                # 绘制5%-95%分位数的线（须线）
                ax.plot([x, x], [mins[j], maxs[j]], color=color, linewidth=1, alpha=0.7)

                # 绘制5%和95%分位数的横线
                ax.plot([x-0.1, x+0.1], [mins[j], mins[j]], color=color, linewidth=2)
                ax.plot([x-0.1, x+0.1], [maxs[j], maxs[j]], color=color, linewidth=2)

                # 绘制25%-75%分位数的框
                box_height = q75s[j] - q25s[j]
                if box_height > 0:
                    rect = plt.Rectangle((x-box_width/2, q25s[j]), box_width, box_height,
                                       facecolor=color, alpha=0.3, edgecolor=color, linewidth=2)
                    ax.add_patch(rect)

                # 绘制中位数线
                ax.plot([x-box_width/2, x+box_width/2], [medians[j], medians[j]],
                       color='darkred', linewidth=3, alpha=0.8)

                # 绘制平均值点
                ax.plot(x, means[j], 'o', color='white', markersize=8,
                       markeredgecolor=color, markeredgewidth=2, zorder=10)

            # 连接中位数点形成微笑曲线
            ax.plot(x_pos, medians, '--', color=color, linewidth=2, alpha=0.8, label='中位数曲线')

            # 连接平均值点
            ax.plot(x_pos, means, '-', color=color, linewidth=2, alpha=0.6, label='平均值曲线')

            ax.set_title(f'{granularity.title()} ({indicator_results[granularity]["n_buckets"]} 分组)')
            ax.set_xlabel('分位数组')
            ax.set_ylabel('收益率 (%)')
            ax.grid(True, alpha=0.3)
            ax.legend(loc='upper right', fontsize=8)

            # 设置x轴标签从1开始
            ax.set_xticks(x_pos)
            ax.set_xticklabels([f'{i+1}' for i in x_pos])

            # 添加图例说明
            legend_text = (
                f'微笑强度: {indicator_results[granularity]["smile_strength"]:.2f}%\n'
                f'━ 5%-95%分位数\n'
                f'▢ 25%-75%分位数\n'
                f'━ 中位数\n'
                f'○ 平均值'
            )
            ax.text(0.02, 0.98, legend_text, transform=ax.transAxes,
                   verticalalignment='top', fontsize=8,
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.9))

        plt.tight_layout()
        safe_name = indicator_name.replace('(', '').replace(')', '').replace(' ', '_')
        plt.savefig(f'{output_dir}/{safe_name}_multi_granularity_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()

def generate_comprehensive_report(all_results: Dict[str, Any], output_dir: str) -> str:
    """
    生成综合分析报告
    """
    report_lines = []

    report_lines.append("# RoC策略 - 细颗粒度因子分析综合报告")
    report_lines.append("")
    report_lines.append("## 🎯 执行摘要")
    report_lines.append("")
    report_lines.append("本报告采用多层次颗粒度分析方法，深入挖掘RoC策略中四个技术指标")
    report_lines.append("（RoC(5)、Max(5)、Vol(5)、Abn_turnover(5)）的微笑曲线效应和交互效应。")
    report_lines.append("相比传统的粗颗粒度分析，本次分析将分组数量从5个增加到20个，")
    report_lines.append("并引入了交互效应、三维分析等高级分析技术。")
    report_lines.append("")

    # 单因子分析结果
    if 'single_factor' in all_results:
        report_lines.append("## 🔍 单因子微笑曲线分析")
        report_lines.append("")

        # 创建颗粒度对比表
        granularity_comparison = []
        for indicator_name, indicator_results in all_results['single_factor'].items():
            for granularity in ['coarse', 'medium', 'fine', 'ultra_fine']:
                if granularity in indicator_results:
                    granularity_comparison.append({
                        '指标': indicator_name,
                        '颗粒度': granularity,
                        '分组数': indicator_results[granularity]['n_buckets'],
                        '微笑强度': f"{indicator_results[granularity]['smile_strength']:.4f}%",
                        '距离相关性': f"{indicator_results[granularity]['distance_correlation']:.4f}",
                        'U型系数': f"{indicator_results[granularity]['u_shape_coefficient']:.6f}",
                        'P值': f"{indicator_results[granularity]['p_value']:.4f}"
                    })

        if granularity_comparison:
            comparison_df = pd.DataFrame(granularity_comparison)
            report_lines.append("### 颗粒度对比分析")
            report_lines.append("")
            report_lines.append(comparison_df.to_markdown(index=False))
            report_lines.append("")

    # 交互效应分析结果
    if 'interaction_2d' in all_results:
        report_lines.append("## 🔄 二维交互效应分析")
        report_lines.append("")

        interaction_summary = []
        for pair, data in all_results['interaction_2d'].items():
            if data is not None:
                interaction_summary.append({
                    '指标组合': pair.replace('_vs_', ' × '),
                    '最佳组合': str(data['best_combination']),
                    '最佳收益率': f"{data['best_return']:.2f}%",
                    '收益率范围': f"{data['return_range']:.2f}%",
                    '交互强度': f"{data['interaction_strength']:.4f}",
                    '有效组合数': data['n_valid_combinations']
                })

        if interaction_summary:
            interaction_df = pd.DataFrame(interaction_summary)
            # 按收益率范围排序
            interaction_df = interaction_df.sort_values('收益率范围', ascending=False)
            report_lines.append(interaction_df.to_markdown(index=False))
            report_lines.append("")

    # 三维交互效应分析结果
    if 'interaction_3d' in all_results:
        report_lines.append("## 🎲 三维交互效应分析")
        report_lines.append("")

        three_d_summary = []
        for combo, data in all_results['interaction_3d'].items():
            three_d_summary.append({
                '三维组合': combo.replace('_', ' × '),
                '最佳三元组合': ' × '.join(data['best_combination']),
                '最高收益率': f"{data['best_return']:.2f}%",
                '收益率范围': f"{data['return_range']:.2f}%",
                '有效组合数': data['n_valid_combinations']
            })

        if three_d_summary:
            three_d_df = pd.DataFrame(three_d_summary)
            three_d_df = three_d_df.sort_values('最高收益率', ascending=False)
            report_lines.append(three_d_df.to_markdown(index=False))
            report_lines.append("")

    # 保存报告
    report_content = '\n'.join(report_lines)
    report_path = os.path.join(output_dir, 'fine_grained_comprehensive_analysis.md')

    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)

    return report_path

def main():
    """
    主函数：执行细颗粒度因子分析
    """
    print("🚀 开始RoC策略细颗粒度因子分析")
    print("="*80)

    # 设置路径
    data_path = 'strategies/RoC/backtest_results/full_market_trades_RoC.csv'
    output_dir = 'strategies/RoC/factor_analysis'

    try:
        # 加载数据
        print("📊 加载交易数据...")
        df = pd.read_csv(data_path)

        # 数据预处理
        print("🔧 数据预处理...")
        df.dropna(subset=['RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)', 'PnL_pct'], inplace=True)
        df = df[df['PnL_pct'] < 500]  # 过滤异常值

        print(f"✅ 有效样本数: {len(df):,}")

        # 定义要分析的指标
        indicators = {
            'RoC(5)': 'RoC(5) - 5日变化率',
            'Max(5)': 'Max(5) - 5日最大收益',
            'Vol(5)': 'Vol(5) - 5日波动率',
            'Abn_turnover(5)': 'Abn_turnover(5) - 异常成交量'
        }

        all_results = {}

        # 1. 单因子细颗粒度分析
        print("\n🔍 执行单因子细颗粒度分析...")
        single_factor_results = {}

        for indicator, indicator_name in indicators.items():
            if indicator not in df.columns:
                print(f"⚠️  警告: 指标 {indicator} 不存在于数据中")
                continue

            print(f"  正在分析 {indicator_name}...")
            indicator_results = analyze_smile_curve_detailed(df, indicator, indicator_name)
            single_factor_results[indicator_name] = indicator_results

            # 打印关键结果
            if 'ultra_fine' in indicator_results:
                ultra_fine = indicator_results['ultra_fine']
                print(f"    超细颗粒度 ({ultra_fine['n_buckets']} 分组):")
                print(f"      微笑强度: {ultra_fine['smile_strength']:.4f}%")
                print(f"      距离相关性: {ultra_fine['distance_correlation']:.4f}")
                print(f"      U型系数: {ultra_fine['u_shape_coefficient']:.6f}")

        all_results['single_factor'] = single_factor_results

        # 2. 二维交互效应分析
        print("\n🔄 执行二维交互效应分析...")
        ultra_fine_2d_results = {}

        indicator_keys = list(indicators.keys())
        for ind1, ind2 in combinations(indicator_keys, 2):
            if ind1 not in df.columns or ind2 not in df.columns:
                continue

            print(f"  分析 {ind1} × {ind2}...")
            result = create_ultra_fine_2d_analysis(df, ind1, ind2, n_bins=10)
            if result:
                ultra_fine_2d_results[f'{ind1}_vs_{ind2}'] = result
                print(f"    最佳收益率: {result['best_return']:.2f}%")
                print(f"    收益率范围: {result['return_range']:.2f}%")

        all_results['interaction_2d'] = ultra_fine_2d_results

        # 3. 三维交互效应分析
        print("\n🎲 执行三维交互效应分析...")
        three_d_results = analyze_three_dimensional_interactions(df, indicator_keys)
        all_results['interaction_3d'] = three_d_results

        if three_d_results:
            print(f"  完成 {len(three_d_results)} 个三维组合分析")
            for combo, data in three_d_results.items():
                print(f"    {combo}: 最佳收益率 {data['best_return']:.2f}%")

        # 4. 生成可视化图表
        print("\n📈 生成可视化图表...")

        # 多颗粒度分析图表
        create_multi_granularity_plots(single_factor_results, output_dir)
        print("  ✅ 多颗粒度分析图表已生成")

        # 交互效应热力图
        if ultra_fine_2d_results:
            create_interaction_heatmaps(ultra_fine_2d_results, output_dir)
            print("  ✅ 交互效应热力图已生成")

        # 5. 生成综合报告
        print("\n📝 生成综合分析报告...")
        report_path = generate_comprehensive_report(all_results, output_dir)
        print(f"  ✅ 报告已保存至: {report_path}")

        # 6. 保存详细数据
        results_path = os.path.join(output_dir, 'fine_grained_analysis_data.pkl')
        with open(results_path, 'wb') as f:
            pickle.dump(all_results, f)
        print(f"  ✅ 详细数据已保存至: {results_path}")

        print("\n" + "="*80)
        print("🎉 细颗粒度因子分析完成！")
        print(f"📊 分析了 {len(df):,} 笔交易")
        print(f"🔍 单因子分析: {len(single_factor_results)} 个指标")
        print(f"🔄 二维交互: {len(ultra_fine_2d_results)} 个组合")
        print(f"🎲 三维交互: {len(three_d_results)} 个组合")
        print("="*80)

    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {data_path}")
        print("请先运行 run_full_market_backtest_RoC.py 生成交易数据。")
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
