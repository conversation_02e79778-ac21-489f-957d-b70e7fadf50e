#!/usr/bin/env python3
"""
RoC策略极值组合分析
借鉴RoC_mod的极值组合发现方法，寻找最优因子组合
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from itertools import combinations, product
import os
import warnings
from typing import Dict, Any, List, Tuple

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def find_extreme_combinations(df: pd.DataFrame, indicators: List[str], n_quantiles: int = 10) -> Dict[str, Any]:
    """
    寻找极值组合：每个因子的极高和极低分位数组合
    """
    extreme_results = {}
    
    # 为每个指标创建分位数标签
    quantile_data = {}
    for indicator in indicators:
        if indicator not in df.columns:
            continue
        
        # 创建分位数分组
        quantiles = pd.qcut(df[indicator], n_quantiles, labels=[f'Q{i+1}' for i in range(n_quantiles)], duplicates='drop')
        quantile_data[indicator] = quantiles
    
    # 分析二维极值组合
    for ind1, ind2 in combinations(indicators, 2):
        if ind1 not in quantile_data or ind2 not in quantile_data:
            continue
        
        # 创建组合分析
        combo_stats = df.groupby([quantile_data[ind1], quantile_data[ind2]], observed=False)['PnL_pct'].agg([
            'count', 'mean', 'std', 'median',
            lambda x: (x > 0).sum() / len(x) if len(x) > 0 else 0  # 胜率
        ]).rename(columns={'<lambda_0>': 'win_rate'})
        
        # 过滤样本数量太少的组合
        valid_combos = combo_stats[combo_stats['count'] >= 20]
        
        if len(valid_combos) > 0:
            # 找到极值组合
            best_combo = valid_combos['mean'].idxmax()
            worst_combo = valid_combos['mean'].idxmin()
            
            # 分析极值组合的特征
            extreme_analysis = {
                'best_combination': best_combo,
                'best_return': valid_combos.loc[best_combo, 'mean'],
                'best_win_rate': valid_combos.loc[best_combo, 'win_rate'],
                'best_count': valid_combos.loc[best_combo, 'count'],
                'worst_combination': worst_combo,
                'worst_return': valid_combos.loc[worst_combo, 'mean'],
                'worst_win_rate': valid_combos.loc[worst_combo, 'win_rate'],
                'worst_count': valid_combos.loc[worst_combo, 'count'],
                'return_spread': valid_combos.loc[best_combo, 'mean'] - valid_combos.loc[worst_combo, 'mean'],
                'all_combinations': valid_combos
            }
            
            extreme_results[f'{ind1}_vs_{ind2}'] = extreme_analysis
    
    return extreme_results

def analyze_factor_regime_performance(df: pd.DataFrame, indicators: List[str]) -> Dict[str, Any]:
    """
    分析不同因子区间下的表现
    """
    regime_results = {}
    
    for indicator in indicators:
        if indicator not in df.columns:
            continue
        
        # 定义不同的区间
        regimes = {
            'extreme_low': df[indicator] <= df[indicator].quantile(0.1),
            'low': (df[indicator] > df[indicator].quantile(0.1)) & (df[indicator] <= df[indicator].quantile(0.3)),
            'medium': (df[indicator] > df[indicator].quantile(0.3)) & (df[indicator] <= df[indicator].quantile(0.7)),
            'high': (df[indicator] > df[indicator].quantile(0.7)) & (df[indicator] <= df[indicator].quantile(0.9)),
            'extreme_high': df[indicator] > df[indicator].quantile(0.9)
        }
        
        regime_stats = {}
        for regime_name, regime_mask in regimes.items():
            regime_data = df[regime_mask]
            if len(regime_data) > 0:
                regime_stats[regime_name] = {
                    'count': len(regime_data),
                    'mean_return': regime_data['PnL_pct'].mean(),
                    'std_return': regime_data['PnL_pct'].std(),
                    'win_rate': (regime_data['PnL_pct'] > 0).mean(),
                    'median_return': regime_data['PnL_pct'].median(),
                    'factor_range': [regime_data[indicator].min(), regime_data[indicator].max()]
                }
        
        regime_results[indicator] = regime_stats
    
    return regime_results

def find_optimal_thresholds(df: pd.DataFrame, indicators: List[str], target_metric: str = 'mean_return') -> Dict[str, Any]:
    """
    寻找每个因子的最优阈值
    """
    optimal_thresholds = {}
    
    for indicator in indicators:
        if indicator not in df.columns:
            continue
        
        # 测试不同的阈值
        thresholds = np.percentile(df[indicator], np.arange(5, 96, 5))
        threshold_results = []
        
        for threshold in thresholds:
            # 高于阈值的组
            above_threshold = df[df[indicator] > threshold]
            # 低于阈值的组
            below_threshold = df[df[indicator] <= threshold]
            
            if len(above_threshold) >= 50 and len(below_threshold) >= 50:
                above_stats = {
                    'threshold': threshold,
                    'direction': 'above',
                    'count': len(above_threshold),
                    'mean_return': above_threshold['PnL_pct'].mean(),
                    'win_rate': (above_threshold['PnL_pct'] > 0).mean(),
                    'std_return': above_threshold['PnL_pct'].std()
                }
                
                below_stats = {
                    'threshold': threshold,
                    'direction': 'below',
                    'count': len(below_threshold),
                    'mean_return': below_threshold['PnL_pct'].mean(),
                    'win_rate': (below_threshold['PnL_pct'] > 0).mean(),
                    'std_return': below_threshold['PnL_pct'].std()
                }
                
                threshold_results.extend([above_stats, below_stats])
        
        if threshold_results:
            # 找到最优阈值
            threshold_df = pd.DataFrame(threshold_results)
            best_threshold = threshold_df.loc[threshold_df[target_metric].idxmax()]
            
            optimal_thresholds[indicator] = {
                'best_threshold': best_threshold['threshold'],
                'best_direction': best_threshold['direction'],
                'best_performance': best_threshold[target_metric],
                'all_results': threshold_df
            }
    
    return optimal_thresholds

def create_extreme_combinations_heatmap(extreme_results: Dict[str, Any], output_dir: str):
    """
    创建极值组合热力图
    """
    # 准备数据
    combination_data = []
    
    for pair, data in extreme_results.items():
        combination_data.append({
            '因子组合': pair.replace('_vs_', ' × '),
            '最佳组合': f"{data['best_combination'][0]} × {data['best_combination'][1]}",
            '最佳收益率': data['best_return'],
            '最佳胜率': data['best_win_rate'],
            '收益率差距': data['return_spread'],
            '样本数': data['best_count']
        })
    
    if not combination_data:
        return
    
    combo_df = pd.DataFrame(combination_data)
    
    # 创建热力图
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('极值组合分析热力图', fontsize=16, fontweight='bold')
    
    # 1. 收益率热力图
    pivot_return = combo_df.pivot_table(values='最佳收益率', index='因子组合', columns='最佳组合', fill_value=0)
    if not pivot_return.empty:
        sns.heatmap(pivot_return, annot=True, fmt='.2f', cmap='RdYlGn', ax=axes[0])
        axes[0].set_title('最佳收益率 (%)')
    
    # 2. 胜率热力图
    pivot_winrate = combo_df.pivot_table(values='最佳胜率', index='因子组合', columns='最佳组合', fill_value=0)
    if not pivot_winrate.empty:
        sns.heatmap(pivot_winrate * 100, annot=True, fmt='.1f', cmap='RdYlGn', ax=axes[1])
        axes[1].set_title('最佳胜率 (%)')
    
    # 3. 收益率差距热力图
    pivot_spread = combo_df.pivot_table(values='收益率差距', index='因子组合', columns='最佳组合', fill_value=0)
    if not pivot_spread.empty:
        sns.heatmap(pivot_spread, annot=True, fmt='.2f', cmap='viridis', ax=axes[2])
        axes[2].set_title('收益率差距 (%)')
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/extreme_combinations_heatmap.png', dpi=300, bbox_inches='tight')
    plt.close()

def generate_extreme_analysis_report(extreme_results: Dict[str, Any], 
                                   regime_results: Dict[str, Any],
                                   optimal_thresholds: Dict[str, Any],
                                   output_dir: str) -> str:
    """
    生成极值分析报告
    """
    report_lines = []
    
    report_lines.append("# RoC策略 - 极值组合深度分析报告")
    report_lines.append("")
    report_lines.append("## 🎯 执行摘要")
    report_lines.append("")
    report_lines.append("本报告专注于发现RoC策略中的极值组合机会，通过分析因子的极端分位数组合、")
    report_lines.append("不同区间表现和最优阈值，寻找能够产生超额收益的特殊组合模式。")
    report_lines.append("")
    
    # 极值组合分析
    if extreme_results:
        report_lines.append("## 🏆 极值组合分析")
        report_lines.append("")
        
        # 按收益率排序
        sorted_combos = sorted(extreme_results.items(), 
                             key=lambda x: x[1]['best_return'], reverse=True)
        
        combo_summary = []
        for pair, data in sorted_combos:
            combo_summary.append({
                '因子组合': pair.replace('_vs_', ' × '),
                '最佳组合': f"{data['best_combination'][0]} × {data['best_combination'][1]}",
                '最佳收益率': f"{data['best_return']:.2f}%",
                '最佳胜率': f"{data['best_win_rate']*100:.1f}%",
                '收益率差距': f"{data['return_spread']:.2f}%",
                '样本数': data['best_count']
            })
        
        combo_df = pd.DataFrame(combo_summary)
        report_lines.append("### 顶级极值组合排名")
        report_lines.append("")
        report_lines.append(combo_df.to_markdown(index=False))
        report_lines.append("")
    
    # 因子区间表现分析
    if regime_results:
        report_lines.append("## 📊 因子区间表现分析")
        report_lines.append("")
        
        for indicator, regimes in regime_results.items():
            report_lines.append(f"### {indicator}")
            report_lines.append("")
            
            regime_summary = []
            for regime_name, stats in regimes.items():
                regime_summary.append({
                    '区间': regime_name,
                    '样本数': stats['count'],
                    '平均收益率': f"{stats['mean_return']:.2f}%",
                    '胜率': f"{stats['win_rate']*100:.1f}%",
                    '收益标准差': f"{stats['std_return']:.2f}%",
                    '因子范围': f"[{stats['factor_range'][0]:.4f}, {stats['factor_range'][1]:.4f}]"
                })
            
            regime_df = pd.DataFrame(regime_summary)
            report_lines.append(regime_df.to_markdown(index=False))
            report_lines.append("")
    
    # 最优阈值分析
    if optimal_thresholds:
        report_lines.append("## 🎯 最优阈值分析")
        report_lines.append("")
        
        threshold_summary = []
        for indicator, threshold_data in optimal_thresholds.items():
            threshold_summary.append({
                '因子': indicator,
                '最优阈值': f"{threshold_data['best_threshold']:.4f}",
                '最优方向': threshold_data['best_direction'],
                '最优表现': f"{threshold_data['best_performance']:.2f}%"
            })
        
        threshold_df = pd.DataFrame(threshold_summary)
        report_lines.append("### 各因子最优阈值")
        report_lines.append("")
        report_lines.append(threshold_df.to_markdown(index=False))
        report_lines.append("")
    
    # 保存报告
    report_content = '\n'.join(report_lines)
    report_path = os.path.join(output_dir, 'extreme_combinations_analysis.md')
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    return report_path

def main():
    """
    主函数：执行极值组合分析
    """
    print("🚀 开始RoC策略极值组合分析")
    print("="*80)

    # 设置路径
    data_path = 'strategies/RoC/backtest_results/full_market_trades_RoC.csv'
    output_dir = 'strategies/RoC/factor_analysis'

    try:
        # 加载数据
        print("📊 加载交易数据...")
        df = pd.read_csv(data_path)

        # 数据预处理
        print("🔧 数据预处理...")
        df.dropna(subset=['RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)', 'PnL_pct'], inplace=True)
        df = df[df['PnL_pct'] < 500]  # 过滤异常值

        print(f"✅ 有效样本数: {len(df):,}")

        # 定义要分析的指标
        indicators = ['RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']

        # 1. 极值组合分析
        print("\n🏆 执行极值组合分析...")
        extreme_results = find_extreme_combinations(df, indicators, n_quantiles=10)

        if extreme_results:
            print(f"  发现 {len(extreme_results)} 个极值组合")
            for pair, data in extreme_results.items():
                print(f"    {pair}: 最佳收益率 {data['best_return']:.2f}%, 差距 {data['return_spread']:.2f}%")

        # 2. 因子区间表现分析
        print("\n📊 执行因子区间表现分析...")
        regime_results = analyze_factor_regime_performance(df, indicators)

        if regime_results:
            print(f"  分析了 {len(regime_results)} 个因子的区间表现")
            for indicator, regimes in regime_results.items():
                best_regime = max(regimes.items(), key=lambda x: x[1]['mean_return'])
                print(f"    {indicator}: 最佳区间 {best_regime[0]} (收益率 {best_regime[1]['mean_return']:.2f}%)")

        # 3. 最优阈值分析
        print("\n🎯 执行最优阈值分析...")
        optimal_thresholds = find_optimal_thresholds(df, indicators, target_metric='mean_return')

        if optimal_thresholds:
            print(f"  找到 {len(optimal_thresholds)} 个因子的最优阈值")
            for indicator, threshold_data in optimal_thresholds.items():
                print(f"    {indicator}: 阈值 {threshold_data['best_threshold']:.4f} ({threshold_data['best_direction']}) "
                      f"-> 收益率 {threshold_data['best_performance']:.2f}%")

        # 4. 生成可视化图表
        print("\n📈 生成可视化图表...")
        if extreme_results:
            create_extreme_combinations_heatmap(extreme_results, output_dir)
            print("  ✅ 极值组合热力图已生成")

        # 5. 生成综合报告
        print("\n📝 生成极值分析报告...")
        report_path = generate_extreme_analysis_report(extreme_results, regime_results,
                                                     optimal_thresholds, output_dir)
        print(f"  ✅ 报告已保存至: {report_path}")

        # 6. 保存详细数据
        import pickle
        results_data = {
            'extreme_combinations': extreme_results,
            'regime_performance': regime_results,
            'optimal_thresholds': optimal_thresholds
        }

        results_path = os.path.join(output_dir, 'extreme_combinations_data.pkl')
        with open(results_path, 'wb') as f:
            pickle.dump(results_data, f)
        print(f"  ✅ 详细数据已保存至: {results_path}")

        print("\n" + "="*80)
        print("🎉 极值组合分析完成！")
        print(f"📊 分析了 {len(df):,} 笔交易")
        print(f"🏆 极值组合: {len(extreme_results)} 个")
        print(f"📊 区间分析: {len(regime_results)} 个因子")
        print(f"🎯 最优阈值: {len(optimal_thresholds)} 个因子")
        print("="*80)

    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {data_path}")
        print("请先运行 run_full_market_backtest_RoC.py 生成交易数据。")
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
