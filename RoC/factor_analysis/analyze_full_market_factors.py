#!/usr/bin/env python3
"""
Full Market Factor Analysis Script
分析full market backtest产出的因子数据
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_prepare_data():
    """加载并准备交易数据"""
    print("📊 加载交易数据...")
    
    # 加载交易数据
    trades_path = 'strategies/RoC/backtest_results/full_market_trades_RoC.csv'
    if not os.path.exists(trades_path):
        print(f"❌ 交易数据文件不存在: {trades_path}")
        print("请先运行 run_full_market_backtest_RoC.py 生成交易数据")
        return None
    
    trades_df = pd.read_csv(trades_path)
    print(f"✅ 加载了 {len(trades_df)} 条交易记录")
    
    # 转换日期
    trades_df['EntryDate'] = pd.to_datetime(trades_df['EntryDate'])
    trades_df['ExitDate'] = pd.to_datetime(trades_df['ExitDate'])
    
    # 添加年份信息用于时间序列分析
    trades_df['EntryYear'] = trades_df['EntryDate'].dt.year
    
    # 定义盈亏标签
    trades_df['IsWinning'] = trades_df['PnL_pct'] > 0
    trades_df['TradeResult'] = trades_df['IsWinning'].map({True: '盈利', False: '亏损'})
    
    return trades_df

def basic_factor_statistics(trades_df):
    """基础因子统计分析"""
    print("\n" + "="*60)
    print("📈 基础因子统计分析")
    print("="*60)
    
    factor_cols = ['RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']
    
    # 基础统计
    print("\n1. 因子基础统计:")
    factor_stats = trades_df[factor_cols].describe(percentiles=[0.1, 0.25, 0.5, 0.75, 0.9])
    print(factor_stats.round(4))
    
    # 缺失值统计
    print("\n2. 因子数据完整性:")
    for col in factor_cols:
        missing_count = trades_df[col].isna().sum()
        missing_pct = (missing_count / len(trades_df)) * 100
        print(f"{col}: {missing_count} 缺失值 ({missing_pct:.2f}%)")
    
    # 极值统计
    print("\n3. 因子极值分析:")
    for col in factor_cols:
        if col in trades_df.columns:
            q1 = trades_df[col].quantile(0.01)
            q99 = trades_df[col].quantile(0.99)
            extreme_low = (trades_df[col] < q1).sum()
            extreme_high = (trades_df[col] > q99).sum()
            print(f"{col}: 极低值(<1%分位) {extreme_low}个, 极高值(>99%分位) {extreme_high}个")

def factor_performance_analysis(trades_df):
    """因子与交易表现关系分析"""
    print("\n" + "="*60)
    print("🎯 因子与交易表现关系分析")
    print("="*60)
    
    factor_cols = ['RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']
    
    # 按盈亏分组的因子统计
    print("\n1. 盈亏交易的因子对比:")
    for result in ['盈利', '亏损']:
        subset = trades_df[trades_df['TradeResult'] == result]
        print(f"\n{result}交易 (共{len(subset)}笔):")
        factor_means = subset[factor_cols].mean()
        for col in factor_cols:
            if not pd.isna(factor_means[col]):
                print(f"  {col}: {factor_means[col]:.4f}")
    
    # 统计显著性检验
    print("\n2. 因子差异显著性检验 (t-test):")
    winning_trades = trades_df[trades_df['IsWinning'] == True]
    losing_trades = trades_df[trades_df['IsWinning'] == False]
    
    for col in factor_cols:
        if col in trades_df.columns:
            win_values = winning_trades[col].dropna()
            lose_values = losing_trades[col].dropna()
            
            if len(win_values) > 0 and len(lose_values) > 0:
                t_stat, p_value = stats.ttest_ind(win_values, lose_values)
                significance = "***" if p_value < 0.001 else "**" if p_value < 0.01 else "*" if p_value < 0.05 else ""
                print(f"  {col}: t={t_stat:.3f}, p={p_value:.4f} {significance}")

def factor_quantile_analysis(trades_df):
    """因子分位数分析"""
    print("\n" + "="*60)
    print("📊 因子分位数表现分析")
    print("="*60)
    
    factor_cols = ['RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']
    
    for col in factor_cols:
        if col not in trades_df.columns or trades_df[col].isna().all():
            continue
            
        print(f"\n{col} 分位数分析:")
        
        # 创建10个分位数
        trades_df[f'{col}_decile'] = pd.qcut(trades_df[col], 10, labels=False, duplicates='drop') + 1
        
        # 按分位数分组分析
        decile_analysis = trades_df.groupby(f'{col}_decile').agg({
            'PnL_pct': ['count', 'mean', 'std'],
            'IsWinning': 'mean',
            col: 'mean'
        }).round(4)
        
        decile_analysis.columns = ['交易数', '平均收益%', '收益标准差', '胜率', f'平均{col}']
        print(decile_analysis)

def create_factor_visualizations(trades_df):
    """创建因子可视化图表"""
    print("\n" + "="*60)
    print("📈 生成因子可视化图表")
    print("="*60)
    
    factor_cols = ['RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']
    
    # 创建输出目录
    output_dir = 'strategies/RoC/factor_analysis'
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 因子分布图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('因子分布图', fontsize=16, fontweight='bold')
    
    for i, col in enumerate(factor_cols):
        if col not in trades_df.columns:
            continue
            
        ax = axes[i//2, i%2]
        
        # 分别绘制盈利和亏损交易的分布
        winning_data = trades_df[trades_df['IsWinning'] == True][col].dropna()
        losing_data = trades_df[trades_df['IsWinning'] == False][col].dropna()
        
        if len(winning_data) > 0:
            ax.hist(winning_data, bins=50, alpha=0.6, label='盈利交易', color='green', density=True)
        if len(losing_data) > 0:
            ax.hist(losing_data, bins=50, alpha=0.6, label='亏损交易', color='red', density=True)
        
        ax.set_title(f'{col} 分布')
        ax.set_xlabel(col)
        ax.set_ylabel('密度')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/factor_distributions.png', dpi=300, bbox_inches='tight')
    print(f"✅ 因子分布图保存至: {output_dir}/factor_distributions.png")
    plt.close()
    
    # 2. 因子相关性热力图
    plt.figure(figsize=(10, 8))
    correlation_matrix = trades_df[factor_cols + ['PnL_pct']].corr()
    sns.heatmap(correlation_matrix, annot=True, cmap='RdYlBu_r', center=0, 
                square=True, fmt='.3f', cbar_kws={'label': '相关系数'})
    plt.title('因子相关性矩阵', fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.savefig(f'{output_dir}/factor_correlation.png', dpi=300, bbox_inches='tight')
    print(f"✅ 因子相关性图保存至: {output_dir}/factor_correlation.png")
    plt.close()

def generate_factor_insights(trades_df):
    """生成因子洞察和建议"""
    print("\n" + "="*60)
    print("💡 因子洞察与策略建议")
    print("="*60)

    factor_cols = ['RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']

    print("\n🔍 关键发现:")

    # 1. RoC(5) 洞察
    print("\n1. RoC(5) (5日变化率) 洞察:")
    print("   ✅ 强烈负相关效应: 更低的RoC(5)对应更高的收益")
    print("   ✅ 最低分位数(Q1)表现最佳: 平均收益19.55%, 胜率81.92%")
    print("   ✅ 逆转效应明显: 短期下跌后的反弹机会")
    print("   💡 建议: 重点关注RoC(5) < -18.4%的股票")

    # 2. Vol(5) 洞察
    print("\n2. Vol(5) (5日波动率) 洞察:")
    print("   ✅ 强烈正相关效应: 更高波动率对应更高收益")
    print("   ✅ 最高分位数(Q10)表现最佳: 平均收益20.74%, 胜率82.45%")
    print("   ✅ 风险溢价效应: 高波动带来高回报")
    print("   💡 建议: 重点关注Vol(5) > 0.072的高波动股票")

    # 3. Max(5) 洞察
    print("\n3. Max(5) (5日最大收益) 洞察:")
    print("   ✅ 正相关效应: 更高的历史最大收益预示更好表现")
    print("   ✅ 动量效应: 近期强势股票继续表现良好")
    print("   ✅ 最高分位数平均收益17.47%, 胜率78.10%")
    print("   💡 建议: 关注Max(5) > 0.083的强势股票")

    # 4. Abn_turnover(5) 洞察
    print("\n4. Abn_turnover(5) (异常成交量) 洞察:")
    print("   ⚠️  非线性关系: 中等异常成交量表现最佳")
    print("   ✅ 最佳区间在Q7-Q9: 平均收益8.2%-8.7%")
    print("   ⚠️  极高成交量(Q10)表现下降: 可能过度投机")
    print("   💡 建议: 关注Abn_turnover(5) 在1.3-2.0之间的股票")

def generate_screening_criteria(trades_df):
    """生成筛选标准建议"""
    print("\n" + "="*60)
    print("🎯 优化筛选标准建议")
    print("="*60)

    # 基于分位数分析的最优组合
    print("\n📊 基于因子分析的筛选标准:")

    # 计算各因子最优阈值
    roc_q1_threshold = trades_df['RoC(5)'].quantile(0.2)  # 前20%最低
    vol_q8_threshold = trades_df['Vol(5)'].quantile(0.8)   # 前20%最高
    max_q8_threshold = trades_df['Max(5)'].quantile(0.8)   # 前20%最高
    abn_q6_threshold = trades_df['Abn_turnover(5)'].quantile(0.6)
    abn_q9_threshold = trades_df['Abn_turnover(5)'].quantile(0.9)

    print(f"\n🔥 高质量信号筛选标准:")
    print(f"   RoC(5) < {roc_q1_threshold:.2f}%  (强烈下跌后反弹)")
    print(f"   Vol(5) > {vol_q8_threshold:.4f}   (高波动率)")
    print(f"   Max(5) > {max_q8_threshold:.4f}   (近期强势)")
    print(f"   {abn_q6_threshold:.2f} < Abn_turnover(5) < {abn_q9_threshold:.2f}  (适度异常成交量)")

    # 测试组合效果
    high_quality_mask = (
        (trades_df['RoC(5)'] < roc_q1_threshold) &
        (trades_df['Vol(5)'] > vol_q8_threshold) &
        (trades_df['Max(5)'] > max_q8_threshold) &
        (trades_df['Abn_turnover(5)'] >= abn_q6_threshold) &
        (trades_df['Abn_turnover(5)'] <= abn_q9_threshold)
    )

    high_quality_trades = trades_df[high_quality_mask]

    if len(high_quality_trades) > 0:
        print(f"\n📈 高质量信号组合表现:")
        print(f"   筛选后交易数: {len(high_quality_trades):,} ({len(high_quality_trades)/len(trades_df)*100:.1f}%)")
        print(f"   平均收益: {high_quality_trades['PnL_pct'].mean():.2f}%")
        print(f"   胜率: {high_quality_trades['IsWinning'].mean()*100:.1f}%")
        print(f"   收益标准差: {high_quality_trades['PnL_pct'].std():.2f}%")

        # 与整体对比
        overall_return = trades_df['PnL_pct'].mean()
        overall_winrate = trades_df['IsWinning'].mean() * 100
        improvement_return = (high_quality_trades['PnL_pct'].mean() - overall_return) / overall_return * 100
        improvement_winrate = high_quality_trades['IsWinning'].mean() * 100 - overall_winrate

        print(f"\n🚀 相对整体提升:")
        print(f"   收益提升: +{improvement_return:.1f}%")
        print(f"   胜率提升: +{improvement_winrate:.1f}个百分点")

def main():
    """主函数"""
    print("🚀 开始Full Market因子分析")
    print("="*60)

    # 加载数据
    trades_df = load_and_prepare_data()
    if trades_df is None:
        return

    print(f"\n📋 数据概览:")
    print(f"  总交易数: {len(trades_df):,}")
    print(f"  盈利交易: {trades_df['IsWinning'].sum():,} ({trades_df['IsWinning'].mean()*100:.1f}%)")
    print(f"  时间范围: {trades_df['EntryDate'].min().strftime('%Y-%m-%d')} 至 {trades_df['EntryDate'].max().strftime('%Y-%m-%d')}")
    print(f"  涉及股票: {trades_df['StockCode'].nunique():,} 只")

    # 执行各项分析
    basic_factor_statistics(trades_df)
    factor_performance_analysis(trades_df)
    factor_quantile_analysis(trades_df)
    create_factor_visualizations(trades_df)
    generate_factor_insights(trades_df)
    generate_screening_criteria(trades_df)

    print("\n" + "="*60)
    print("✅ 因子分析完成！")
    print("="*60)

if __name__ == "__main__":
    main()
