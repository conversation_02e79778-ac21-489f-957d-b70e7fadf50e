#!/usr/bin/env python3
"""
Update Optimized Parameters for Hong Kong RoC Strategy

This script reads the latest optimization results and updates the strategy parameters
in the main Hong Kong backtest files.
"""

import sys
import os
import json
import glob
import re
from datetime import datetime

# Path setup
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def find_latest_optimization_results():
    """Find the latest optimization results file"""
    results_dir = os.path.join(os.path.dirname(__file__), 'backtest_results')
    
    # Look for best parameters files
    pattern = os.path.join(results_dir, 'best_parameters_*_hk_*.json')
    files = glob.glob(pattern)
    
    if not files:
        print("❌ No optimization results found!")
        print(f"Looking in: {results_dir}")
        print("Please run the optimization script first.")
        return None
    
    # Sort by timestamp in filename
    files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    latest_file = files[0]
    
    print(f"📁 Found latest optimization results: {os.path.basename(latest_file)}")
    return latest_file

def load_optimization_results(file_path):
    """Load optimization results from JSON file"""
    try:
        with open(file_path, 'r') as f:
            results = json.load(f)
        
        print("📊 Optimization Results Summary:")
        print(f"   Timestamp: {results.get('optimization_timestamp', 'Unknown')}")
        print(f"   Market: {results.get('market', 'Unknown')}")
        print(f"   Best Sharpe Ratio: {results.get('best_sharpe_ratio', 'Unknown'):.4f}")
        print(f"   Combinations Tested: {results.get('combinations_tested', 'Unknown')}")
        print(f"   In-Sample Period: {results.get('in_sample_period', 'Unknown')}")
        
        return results.get('best_parameters', {})
        
    except Exception as e:
        print(f"❌ Error loading optimization results: {e}")
        return None

def update_strategy_parameters(file_path, new_params):
    """Update strategy parameters in a Python file"""
    try:
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Backup the original file
        backup_path = file_path + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"💾 Backup created: {os.path.basename(backup_path)}")
        
        # Update each parameter
        updated_content = content
        for param_name, param_value in new_params.items():
            # Pattern to match parameter assignment
            pattern = rf"('{param_name}':\s*)[^,\}}]+(,|\s*\}})"
            replacement = rf"\g<1>{param_value}\g<2>"
            
            new_content = re.sub(pattern, replacement, updated_content)
            if new_content != updated_content:
                print(f"✅ Updated {param_name}: {param_value}")
                updated_content = new_content
            else:
                print(f"⚠️  Could not find parameter {param_name} in file")
        
        # Write the updated content
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        return True
        
    except Exception as e:
        print(f"❌ Error updating {file_path}: {e}")
        return False

def main():
    """Main function"""
    print("="*80)
    print("🔧 UPDATING OPTIMIZED PARAMETERS FOR HONG KONG ROC STRATEGY")
    print("="*80)
    
    # Find latest optimization results
    results_file = find_latest_optimization_results()
    if not results_file:
        return
    
    # Load optimization results
    best_params = load_optimization_results(results_file)
    if not best_params:
        return
    
    print("\n🎯 Best Parameters Found:")
    for param, value in best_params.items():
        print(f"   {param}: {value}")
    
    # Confirm with user
    response = input("\n❓ Do you want to update the strategy files with these parameters? (y/n): ")
    if response.lower() != 'y':
        print("❌ Update cancelled by user")
        return
    
    # Files to update
    files_to_update = [
        os.path.join(os.path.dirname(__file__), 'run_full_market_backtest_RoC_HK.py')
    ]
    
    print("\n🔄 Updating strategy files...")
    
    success_count = 0
    for file_path in files_to_update:
        if os.path.exists(file_path):
            print(f"\n📝 Updating {os.path.basename(file_path)}...")
            if update_strategy_parameters(file_path, best_params):
                success_count += 1
            else:
                print(f"❌ Failed to update {os.path.basename(file_path)}")
        else:
            print(f"⚠️  File not found: {os.path.basename(file_path)}")
    
    print("\n" + "="*80)
    if success_count > 0:
        print("🎉 PARAMETER UPDATE COMPLETED!")
        print("="*80)
        print(f"✅ Successfully updated {success_count} file(s)")
        print("\nNext steps:")
        print("1. Run the Hong Kong full market backtest to generate new trades")
        print("2. Run the Hong Kong portfolio backtest to verify improvements")
        print("3. Compare in-sample and out-of-sample performance")
        
        print("\nCommands to run:")
        print("cd RoC")
        print("python run_full_market_backtest_RoC_HK.py")
        print("python run_portfolio_backtest_RoC_HK.py")
    else:
        print("❌ NO FILES WERE UPDATED")
        print("="*80)
        print("Please check the error messages above and try again.")

if __name__ == "__main__":
    main()
