# Hong Kong RoC Strategy Parameter Optimization

This directory contains tools for optimizing the RoC strategy parameters specifically for Hong Kong market data to maximize in-sample Sharpe ratio.

## Overview

The optimization system tests different parameter combinations for the Hong Kong RoC strategy and identifies the settings that produce the highest Sharpe ratio during the in-sample period (2010-2023). This helps improve strategy performance by finding optimal parameter values for the Hong Kong market.

## Files

- `optimize_7h_hk.py` - Core Hong Kong optimization engine
- `run_optimization_hk.py` - User-friendly interface with presets for Hong Kong
- `update_optimized_parameters_hk.py` - Script to apply optimized parameters
- `README_optimization_hk.md` - This documentation file
- `backtest_results/` - Directory where Hong Kong optimization results are saved

## Quick Start

### Option 1: Use Presets (Recommended)

```bash
# Quick 2-hour optimization (for testing)
python run_optimization_hk.py --preset quick

# Balanced 7-hour optimization (recommended)
python run_optimization_hk.py --preset balanced

# Thorough 12-hour optimization (best results)
python run_optimization_hk.py --preset thorough
```

### Option 2: Interactive Mode

```bash
python run_optimization_hk.py
```

This will show you all available presets and let you choose interactively.

### Option 3: Custom Duration

```bash
python run_optimization_hk.py --preset custom
```

This allows you to specify a custom optimization duration.

### Option 4: Direct Script Usage

```bash
# Run optimization for specific duration
python optimize_7h_hk.py --hours 7.0
```

## Complete Workflow

### 1. Run Optimization

```bash
# Choose your preferred method
python run_optimization_hk.py --preset balanced
```

### 2. Apply Optimized Parameters

```bash
# This will update the strategy files with the best parameters found
python update_optimized_parameters_hk.py
```

### 3. Test the Optimized Strategy

```bash
# Generate new trades with optimized parameters
python run_full_market_backtest_RoC_HK.py

# Run portfolio backtest to see performance
python run_portfolio_backtest_RoC_HK.py
```

## Parameter Space

The optimization tests the following parameters:

- **roc_period**: [25, 30, 35] - Rate of change calculation period
- **roc_std_window**: [600, 750, 900] - Standard deviation window for RoC
- **roc_std_multiplier**: [1.75, 2.0, 2.25, 2.5] - Multiplier for RoC threshold
- **profit_target_atr_multiplier**: [1.75, 2.0, 2.5] - Profit target based on ATR
- **stop_loss_atr_multiplier**: [1.25, 1.5, 1.75] - Stop loss based on ATR
- **max_hold_days**: [25, 30, 35] - Maximum holding period

## Optimization Strategy

The optimizer uses a smart sampling approach:

1. **Current Best**: Always includes the current best-known parameters
2. **Edge Cases**: Tests extreme parameter values
3. **Random Sampling**: Fills remaining budget with random combinations
4. **Time Management**: Automatically adjusts the number of combinations based on target duration

## Prerequisites

### Required Files
- `run_full_market_backtest_RoC_HK.py` - Hong Kong full market backtest
- `run_portfolio_backtest_RoC_HK.py` - Hong Kong portfolio backtest
- `data/h_shares_daily.parquet` - Hong Kong market data

### Optional Files
- `data/hsi_daily.csv` - Hang Seng Index benchmark data (will create simple benchmark if missing)

## Output Files

After optimization, you'll find these files in `backtest_results/`:

- `quick_7h_optimization_hk_YYYYMMDD_HHMMSS.csv` - Detailed results for all tested combinations
- `best_parameters_7h_hk_YYYYMMDD_HHMMSS.json` - Best parameters and summary statistics

## Performance Considerations

- **Quick (2 hours)**: ~60 combinations, good for testing
- **Balanced (7 hours)**: ~210 combinations, recommended for most users
- **Thorough (12 hours)**: ~360 combinations, best results but longer runtime

Each combination takes approximately 2 minutes to evaluate, including:
- Full market backtest generation
- Portfolio simulation
- Performance metric calculation

## Validation Workflow

After applying optimized parameters:

1. **In-Sample Verification**: Confirm improved Sharpe ratio on 2010-2023 data
2. **Out-of-Sample Testing**: Test on 2024+ data to check for overfitting
3. **Robustness Check**: Ensure parameters work across different market conditions

## Example Complete Session

```bash
# 1. Run optimization (choose balanced preset)
python run_optimization_hk.py --preset balanced

# 2. Wait for completion (about 7 hours)
# The script will show progress and best parameters found

# 3. Apply the optimized parameters
python update_optimized_parameters_hk.py

# 4. Test the optimized strategy
python run_full_market_backtest_RoC_HK.py
python run_portfolio_backtest_RoC_HK.py

# 5. Compare results with previous performance
```

## Troubleshooting

### Common Issues

1. **Missing Data Files**: Ensure `data/h_shares_daily.parquet` exists
2. **Import Errors**: Check that all Hong Kong strategy files are present
3. **Memory Issues**: Use the quick preset for systems with limited RAM
4. **Long Runtime**: Consider using the quick preset first to test the setup

### Performance Tips

- Run optimization during off-hours due to long duration
- Monitor system resources during optimization
- Use SSD storage for better I/O performance
- Close other applications to free up memory

## Advanced Usage

### Custom Parameter Space

To modify the parameter space, edit the `get_smart_parameter_combinations()` method in `optimize_7h_hk.py`:

```python
param_space = {
    'roc_period': [20, 25, 30, 35, 40],  # Add more values
    'roc_std_window': [500, 600, 750, 900, 1000],  # Expand range
    # ... other parameters
}
```

### Custom Optimization Logic

The optimization objective can be modified in the `evaluate_parameters()` method to optimize for different metrics (e.g., CAGR, max drawdown, etc.).

This optimization system helps systematically improve the Hong Kong RoC strategy performance by finding the parameter combination that maximizes in-sample Sharpe ratio specifically for the Hong Kong market.
