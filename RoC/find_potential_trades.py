import sys
import os
import pandas as pd
from tqdm import tqdm

# Configure tqdm to work with pandas apply
tqdm.pandas()

# --- Path Setup ---
# Add the project root to the Python path to resolve import issues
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import from utilities
from utilities.utils import load_stock_names

# --- Strategy Configuration ---
LOOKBACK_DAYS = 10

# --- Helper to Load HK Stock Names ---
def load_hk_stock_names(df: pd.DataFrame) -> dict:
    """
    从港股数据中提取股票代码和名称的映射关系
    """
    stock_names = {}
    try:
        # 从数据中提取唯一的股票代码和名称组合
        unique_stocks = df[['code', 'name']].drop_duplicates()
        for _, row in unique_stocks.iterrows():
            # 将HK.xxxxx格式转换为xxxxx格式
            stock_code = row['code'].replace('HK.', '') if row['code'].startswith('HK.') else row['code']
            stock_names[stock_code] = row['name']
        print(f"成功加载 {len(stock_names)} 个港股名称")
    except Exception as e:
        print(f"加载港股名称时出错: {e}")
    return stock_names

# --- Helper to Analyze HSGT Holdings ---
def analyze_hsgt_holdings_change(df: pd.DataFrame, days: int = 5) -> pd.DataFrame:
    """
    分析港股通持股比例变化，找出最近N日增加最大的公司
    """
    print(f"分析最近{days}日港股通持股比例变化...")

    # 过滤有港股通数据的记录
    hsgt_data = df[df['hsgt_holding_ratio'].notna()].copy()

    if hsgt_data.empty:
        print("未找到港股通持股数据")
        return pd.DataFrame()

    # 按股票代码分组，计算每只股票最近N日的持股比例变化
    latest_date = hsgt_data['time_key'].max()
    start_date = latest_date - pd.Timedelta(days=days)

    recent_data = hsgt_data[hsgt_data['time_key'] >= start_date].copy()

    results = []
    for code in recent_data['code'].unique():
        stock_data = recent_data[recent_data['code'] == code].sort_values('time_key')

        if len(stock_data) < 2:
            continue

        # 计算持股比例变化
        latest_ratio = stock_data['hsgt_holding_ratio'].iloc[-1]
        earliest_ratio = stock_data['hsgt_holding_ratio'].iloc[0]

        if pd.notna(latest_ratio) and pd.notna(earliest_ratio):
            ratio_change = latest_ratio - earliest_ratio

            results.append({
                'code': code,
                'name': stock_data['name'].iloc[-1],
                'latest_date': stock_data['time_key'].iloc[-1].strftime('%Y-%m-%d'),
                'earliest_ratio': earliest_ratio,
                'latest_ratio': latest_ratio,
                'ratio_change': ratio_change,
                'ratio_change_pct': (ratio_change / earliest_ratio * 100) if earliest_ratio > 0 else 0,
                'latest_holding_value': stock_data['hsgt_holding_value'].iloc[-1] if 'hsgt_holding_value' in stock_data.columns else None,
                'latest_close': stock_data['close'].iloc[-1]
            })

    if not results:
        print("未找到有效的港股通持股比例变化数据")
        return pd.DataFrame()

    # 转换为DataFrame并按持股比例变化排序
    result_df = pd.DataFrame(results)
    result_df = result_df.sort_values('ratio_change', ascending=False)

    return result_df

# --- Core Screener Function ---
def get_quantile_position(value: float, indicator: str) -> int:
    """
    根据历史in-sample数据确定指标值所在的分位数位置 (Q1-Q10)
    基于RoC_mod策略分析的分位数阈值
    """
    # 基于in-sample (2010-2023) 历史数据的分位数阈值
    quantile_thresholds = {
        'RoC5': [-15.2, -8.1, -4.2, -1.8, 0.5, 2.8, 5.4, 8.9, 14.2],
        'Max5': [0.012, 0.018, 0.024, 0.031, 0.039, 0.048, 0.059, 0.073, 0.095],
        'Vol5': [0.0075, 0.012, 0.016, 0.021, 0.026, 0.032, 0.039, 0.048, 0.062],
        'Abn_turnover5': [0.15, 0.25, 0.35, 0.48, 0.65, 0.88, 1.2, 1.7, 2.5]
    }

    if indicator not in quantile_thresholds:
        return 5  # 默认中位数

    thresholds = quantile_thresholds[indicator]
    for i, threshold in enumerate(thresholds):
        if value <= threshold:
            return i + 1
    return 10

def evaluate_favorable_conditions(roc5_q: int, vol5_q: int, max5_q: int, abn5_q: int) -> tuple:
    """
    基于RoC_mod策略细颗粒度分析评估有利条件
    返回: (优先级, 条件描述, 是否高亮)

    优先级排序 (基于分析结果):
    1. 第一优先: Vol(5) Q10 (风险低，收益高)
    2. 第二优先: RoC(5) Q1/Q10 + ABN5 Q1强化 (最佳表现区域)
    3. 第三优先: RoC(5) Q1/Q10 单独 (潜力大)
    4. 第四优先: Max(5) Q10 (权重增强)

    ABN5不单独作为条件，而是作为ROC5的强化因子
    """
    conditions = []
    priority = 5  # 默认最低优先级

    # 第一优先级: Vol(5) Q10 - 最有利条件
    if vol5_q == 10:
        conditions.append("🔥VOL5_Q10")
        priority = min(priority, 1)

    # 第二优先级: ROC5极值 + ABN5 Q1强化 (左上角和左下角最佳表现区域)
    if (roc5_q == 1 or roc5_q == 10) and abn5_q == 1:
        if roc5_q == 1:
            conditions.append("🚀ROC5_Q1+ABN_Q1")  # 左上角: 强烈下跌后反弹 + 低异常换手率
        else:
            conditions.append("🚀ROC5_Q10+ABN_Q1")  # 左下角: 强势上涨 + 低异常换手率
        priority = min(priority, 2)

    # 第三优先级: ROC5极值单独 (没有ABN强化)
    elif roc5_q == 1:
        conditions.append("⭐ROC5_Q1")
        priority = min(priority, 3)
    elif roc5_q == 10:
        conditions.append("⭐ROC5_Q10")
        priority = min(priority, 3)

    # 第四优先级: Max(5) Q10 - 权重增强条件
    if max5_q == 10:
        conditions.append("📈MAX5_Q10")
        priority = min(priority, 4)

    # 特殊组合条件 - Vol(5) Q10 + ROC5极值 + ABN5 Q1 (三重强化)
    if vol5_q == 10 and (roc5_q == 1 or roc5_q == 10) and abn5_q == 1:
        conditions.append("💎TRIPLE")
        priority = min(priority, 1)

    condition_str = " ".join(conditions) if conditions else ""
    is_favorable = len(conditions) > 0

    return priority, condition_str, is_favorable

def check_entry_condition(stock_df: pd.DataFrame, params: dict) -> pd.DataFrame:
    """
    Checks if a single stock meets the entry criteria within the lookback period.
    Designed to be used with pandas groupby().apply().
    Returns a DataFrame of all signals found, or None.
    """
    # Ensure data is sorted and sufficient
    stock_df = stock_df.sort_index()
    if len(stock_df) < params['roc_std_window'] + 5: # Need enough data
        return None

    # --- Perform all calculations inside the function to ensure correctness ---
    # Condition 1: 1.7x ROC 40
    roc40_period = 40
    roc40_std_multiplier = 1.7
    roc40 = (stock_df['Close'] / stock_df['Close'].shift(roc40_period) - 1) * 100
    roc40_prev = roc40.shift(1)
    roc40_std = roc40.rolling(window=params['roc_std_window']).std()
    threshold40 = -(roc40_std * roc40_std_multiplier)

    # Condition 2: 2x ROC 30
    roc30_period = 30
    roc30_std_multiplier = 2.0
    roc30 = (stock_df['Close'] / stock_df['Close'].shift(roc30_period) - 1) * 100
    roc30_prev = roc30.shift(1)
    roc30_std = roc30.rolling(window=params['roc_std_window']).std()
    threshold30 = -(roc30_std * roc30_std_multiplier)

    # --- Calculate additional indicators for quantile analysis ---
    # ROC5: 5-day rate of change
    roc5 = ((stock_df['Close'] - stock_df['Close'].shift(5)) / stock_df['Close'].shift(5)) * 100

    # Returns for other calculations
    returns = stock_df['Close'].pct_change()

    # Max5: 5-day maximum daily return
    max5 = returns.rolling(window=5, min_periods=1).max()

    # Vol5: 5-day rolling standard deviation of daily returns
    vol5 = returns.rolling(window=5, min_periods=1).std()

    # Abn_turnover5: abnormal turnover (5-day mean / 250-day mean)
    # Note: Using Close as proxy for volume since volume data might not be available
    if 'Volume' in stock_df.columns and not stock_df['Volume'].isna().all():
        abn_turnover5 = (
            stock_df['Volume'].rolling(window=5, min_periods=1).mean() /
            stock_df['Volume'].rolling(window=250, min_periods=50).mean()
        )
    else:
        # Use price-based proxy if volume not available
        price_activity = stock_df['Close'].rolling(window=5).std()
        price_baseline = stock_df['Close'].rolling(window=250, min_periods=50).std()
        abn_turnover5 = price_activity / price_baseline
        abn_turnover5 = abn_turnover5.fillna(1.0)
    
    # --- Entry Trigger Conditions (Vectorized) ---
    # Break down triggers for reporting purposes
    trigger_roc40_thresh = (roc40 < threshold40) & (roc40 > roc40_prev)
    trigger_roc30_thresh = (roc30 < threshold30) & (roc30 > roc30_prev)
    trigger_roc_abs = (roc40 < -20) & (roc40 > roc40_prev) # Based on original logic using ROC40
    # 新增的绝对值条件
    trigger_roc30_abs = (roc30 < -20) & (roc30 > roc30_prev)  # ROC30 < -20
    trigger_roc40_abs_strict = (roc40 < -20) & (roc40 > roc40_prev)  # ROC40 < -20 (更严格的条件)

    is_triggered = trigger_roc40_thresh | trigger_roc30_thresh | trigger_roc_abs | trigger_roc30_abs | trigger_roc40_abs_strict

    # Get the signals within the lookback period
    lookback_days = params['lookback_days']
    recent_triggers = is_triggered.tail(lookback_days)
    triggered_dates = recent_triggers[recent_triggers].index

    if not triggered_dates.empty:
        results = []
        for trade_date in triggered_dates:
            roc40_val = roc40.get(trade_date)
            threshold40_val = threshold40.get(trade_date)
            roc30_val = roc30.get(trade_date)
            threshold30_val = threshold30.get(trade_date)

            # Get additional indicator values for quantile analysis
            roc5_val = roc5.get(trade_date)
            max5_val = max5.get(trade_date)
            vol5_val = vol5.get(trade_date)
            abn5_val = abn_turnover5.get(trade_date)

            if pd.isna(roc40_val) or pd.isna(threshold40_val) or pd.isna(roc30_val) or pd.isna(threshold30_val):
                continue

            reasons = []
            # Check which condition(s) triggered the signal
            if trigger_roc40_thresh.get(trade_date):
                reasons.append("1.7x ROC 40")
            elif trigger_roc_abs.get(trade_date): # Use elif to make it mutually exclusive with the more specific threshold condition
                reasons.append("ROC40 < -20")
            elif trigger_roc40_abs_strict.get(trade_date):
                reasons.append("ROC40 < -20 (strict)")

            if trigger_roc30_thresh.get(trade_date):
                reasons.append("2x ROC 30")
            elif trigger_roc30_abs.get(trade_date):
                reasons.append("ROC30 < -20")

            if not reasons:
                continue

            # Calculate quantile positions
            roc5_q = get_quantile_position(roc5_val if not pd.isna(roc5_val) else 0, 'RoC5')
            max5_q = get_quantile_position(max5_val if not pd.isna(max5_val) else 0, 'Max5')
            vol5_q = get_quantile_position(vol5_val if not pd.isna(vol5_val) else 0, 'Vol5')
            abn5_q = get_quantile_position(abn5_val if not pd.isna(abn5_val) else 1, 'Abn_turnover5')

            # Evaluate favorable conditions based on fine-grained analysis
            priority, favorable_conditions, is_favorable = evaluate_favorable_conditions(roc5_q, vol5_q, max5_q, abn5_q)

            results.append({
                'Date': trade_date.strftime('%Y-%m-%d'),
                'Close': stock_df.loc[trade_date, 'Close'],
                'Reason': ", ".join(reasons),
                'ROC(40)': f"{roc40_val:.2f}%",
                'Thresh(40)': f"{threshold40_val:.2f}%",
                'ROC(30)': f"{roc30_val:.2f}%",
                'Thresh(30)': f"{threshold30_val:.2f}%",
                'ROC5': f"{roc5_val:.2f}%" if not pd.isna(roc5_val) else "N/A",
                'ROC5_Q': f"Q{roc5_q}",
                'MAX5': f"{max5_val:.4f}" if not pd.isna(max5_val) else "N/A",
                'MAX5_Q': f"Q{max5_q}",
                'VOL5': f"{vol5_val:.4f}" if not pd.isna(vol5_val) else "N/A",
                'VOL5_Q': f"Q{vol5_q}",
                'ABN5': f"{abn5_val:.3f}" if not pd.isna(abn5_val) else "N/A",
                'ABN5_Q': f"Q{abn5_q}",
                'Priority': priority,
                'Favorable_Conditions': favorable_conditions,
                'Is_Favorable': is_favorable,
            })

        if not results:
            return None

        return pd.DataFrame(results)

    return None


def find_potential_trades(parquet_path: str, strategy_params: dict):
    """
    Scans a market-wide Parquet file to find stocks meeting entry criteria on the latest date.
    """
    print(f"Loading data from {parquet_path}...")
    try:
        df = pd.read_parquet(parquet_path)
    except Exception as e:
        print(f"Error reading Parquet file: {e}")
        return

    print("Data loaded. Preparing for analysis...")

    # --- Data Preparation for HK Stocks ---
    # 港股数据列名映射
    rename_map = {
        'time_key': 'Date',
        'code': 'StockCode',
        'close': 'Close'
    }
    df.rename(columns={k: v for k, v in rename_map.items() if k in df.columns}, inplace=True)

    required_cols = ['Date', 'StockCode', 'Close']
    if not all(col in df.columns for col in required_cols):
        print(f"Error: Parquet file must contain columns: {required_cols}. Found: {df.columns.tolist()}")
        return

    # 处理港股代码格式 (HK.xxxxx -> xxxxx)
    if 'StockCode' in df.columns:
        df['StockCode'] = df['StockCode'].astype(str).str.replace('HK.', '', regex=False)

    df['Date'] = pd.to_datetime(df['Date'])
    df.set_index('Date', inplace=True)

    # --- Load HK Stock Names ---
    print("Loading HK stock names...")
    # 重新读取原始数据来获取股票名称
    original_df = pd.read_parquet(parquet_path)
    stock_names = load_hk_stock_names(original_df)

    original_stock_count = df['StockCode'].nunique()
    # 只保留有名称的股票
    df = df[df['StockCode'].isin(stock_names.keys())]

    excluded_count = original_stock_count - df['StockCode'].nunique()
    if excluded_count > 0:
        print(f"-> Excluded {excluded_count} stocks without valid names.")

    # --- Run the Screener ---
    print(f"Screening {df['StockCode'].nunique()} stocks for signals...")
    
    potential_trades = df.groupby('StockCode').progress_apply(check_entry_condition, params=strategy_params, include_groups=False)
    
    if isinstance(potential_trades, pd.Series) and potential_trades.empty:
        potential_trades = pd.DataFrame()

    if not potential_trades.empty:
        potential_trades.reset_index(inplace=True)
        potential_trades.rename(columns={'level_0': 'StockCode'}, inplace=True)
        if 'level_1' in potential_trades.columns:
            potential_trades.drop(columns=['level_1'], inplace=True)

    # --- Add Stock Names to Results ---
    if not potential_trades.empty:
        # Sort by priority first (lower number = higher priority), then by date
        potential_trades.sort_values(by=['Priority', 'Date'], ascending=[True, False], inplace=True)

        potential_trades['StockName'] = potential_trades['StockCode'].map(
            lambda x: stock_names.get(str(x), 'Unknown')
        )

        # Reorder columns for clarity - put favorable conditions first
        potential_trades = potential_trades[[
            'Priority', 'Favorable_Conditions', 'StockCode', 'StockName', 'Date', 'Close', 'Reason',
            'ROC(40)', 'Thresh(40)', 'ROC(30)', 'Thresh(30)',
            'ROC5', 'ROC5_Q', 'MAX5', 'MAX5_Q', 'VOL5', 'VOL5_Q', 'ABN5', 'ABN5_Q', 'Is_Favorable'
        ]]

    # --- Save Results to CSV ---
    output_dir = os.path.join(os.path.dirname(__file__), 'backtest_results')
    os.makedirs(output_dir, exist_ok=True)

    # Generate filename with current date
    from datetime import datetime
    current_date = datetime.now().strftime('%Y%m%d_%H%M%S')
    csv_filename = f'hk_potential_trades_{current_date}.csv'
    csv_path = os.path.join(output_dir, csv_filename)

    # --- Display Results ---
    print("\n" + "="*80)
    print(f"           Potential Trades Found in the Last {strategy_params['lookback_days']} Days")
    print("="*80)

    if potential_trades.empty:
        print("No stocks met the entry criteria in the lookback period.")
        # Save empty CSV with headers
        empty_df = pd.DataFrame(columns=[
            'Priority', 'Favorable_Conditions', 'StockCode', 'StockName', 'Date', 'Close', 'Reason',
            'ROC(40)', 'Thresh(40)', 'ROC(30)', 'Thresh(30)',
            'ROC5', 'ROC5_Q', 'MAX5', 'MAX5_Q', 'VOL5', 'VOL5_Q', 'ABN5', 'ABN5_Q', 'Is_Favorable'
        ])
        empty_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"\nEmpty results saved to: {csv_path}")
    else:
        # Separate favorable and regular trades for better display
        favorable_trades = potential_trades[potential_trades['Is_Favorable'] == True]
        regular_trades = potential_trades[potential_trades['Is_Favorable'] == False]

        if not favorable_trades.empty:
            print("\n🌟 HIGH PRIORITY TRADES (Favorable Conditions):")
            print("="*120)
            print(favorable_trades.to_string(index=False))
            print(f"\n⭐ Found {len(favorable_trades)} HIGH PRIORITY trades with favorable conditions!")

        if not regular_trades.empty:
            print(f"\n📊 Regular Trades ({len(regular_trades)} found):")
            print("-"*120)
            print(regular_trades.to_string(index=False))

        print("\n" + "="*120)
        print(f"SUMMARY: Found {len(potential_trades)} total potential trades")
        if not favorable_trades.empty:
            print(f"         🔥 {len(favorable_trades)} HIGH PRIORITY trades with favorable conditions")
            print(f"         📊 {len(regular_trades)} regular trades")

            # Show condition breakdown
            condition_counts = {}
            for conditions in favorable_trades['Favorable_Conditions']:
                for condition in conditions.split():
                    condition_counts[condition] = condition_counts.get(condition, 0) + 1

            if condition_counts:
                print(f"\n🎯 Favorable Condition Breakdown:")
                for condition, count in sorted(condition_counts.items(), key=lambda x: x[1], reverse=True):
                    print(f"   {condition}: {count} trades")

        # Save to CSV
        potential_trades.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"\nResults saved to: {csv_path}")

    print("="*80)

    # --- 分析港股通持股比例变化 ---
    print("\n" + "="*80)
    print("           港股通持股比例变化分析 (最近5日)")
    print("="*80)

    # 重新读取原始数据进行港股通分析
    original_df = pd.read_parquet(parquet_path)
    hsgt_analysis = analyze_hsgt_holdings_change(original_df, days=5)

    if not hsgt_analysis.empty:
        # 显示持股比例增加最多的前10名
        top_10_increase = hsgt_analysis.head(10)
        print("\n🔥 港股通持股比例增加最大的10家公司:")
        print("="*120)

        # 格式化显示增加最多的
        display_cols = ['code', 'name', 'latest_date', 'earliest_ratio', 'latest_ratio',
                       'ratio_change', 'ratio_change_pct', 'latest_close']

        formatted_increase = top_10_increase[display_cols].copy()
        formatted_increase['earliest_ratio'] = formatted_increase['earliest_ratio'].round(2)
        formatted_increase['latest_ratio'] = formatted_increase['latest_ratio'].round(2)
        formatted_increase['ratio_change'] = formatted_increase['ratio_change'].round(2)
        formatted_increase['ratio_change_pct'] = formatted_increase['ratio_change_pct'].round(2)
        formatted_increase['latest_close'] = formatted_increase['latest_close'].round(2)

        # 重命名列名为中文
        formatted_increase.columns = ['股票代码', '股票名称', '最新日期', '期初持股比例(%)',
                                     '最新持股比例(%)', '持股比例变化', '变化百分比(%)', '最新收盘价']

        print(formatted_increase.to_string(index=False))

        # 显示持股比例减少最多的前10名
        bottom_10_decrease = hsgt_analysis.tail(10).iloc[::-1]  # 取最后10个并反转顺序，使减少最多的在前
        print("\n📉 港股通持股比例减少最大的10家公司:")
        print("="*120)

        formatted_decrease = bottom_10_decrease[display_cols].copy()
        formatted_decrease['earliest_ratio'] = formatted_decrease['earliest_ratio'].round(2)
        formatted_decrease['latest_ratio'] = formatted_decrease['latest_ratio'].round(2)
        formatted_decrease['ratio_change'] = formatted_decrease['ratio_change'].round(2)
        formatted_decrease['ratio_change_pct'] = formatted_decrease['ratio_change_pct'].round(2)
        formatted_decrease['latest_close'] = formatted_decrease['latest_close'].round(2)

        # 重命名列名为中文
        formatted_decrease.columns = ['股票代码', '股票名称', '最新日期', '期初持股比例(%)',
                                     '最新持股比例(%)', '持股比例变化', '变化百分比(%)', '最新收盘价']

        print(formatted_decrease.to_string(index=False))

        # 统计信息
        total_increase_count = len(hsgt_analysis[hsgt_analysis['ratio_change'] > 0])
        total_decrease_count = len(hsgt_analysis[hsgt_analysis['ratio_change'] < 0])
        avg_change = hsgt_analysis['ratio_change'].mean()

        print(f"\n📊 统计信息:")
        print(f"   持股比例增加的公司: {total_increase_count} 家")
        print(f"   持股比例减少的公司: {total_decrease_count} 家")
        print(f"   平均持股比例变化: {avg_change:.2f}%")

        # 保存港股通分析结果
        hsgt_csv_filename = f'hsgt_holdings_change_{current_date}.csv'
        hsgt_csv_path = os.path.join(output_dir, hsgt_csv_filename)
        hsgt_analysis.to_csv(hsgt_csv_path, index=False, encoding='utf-8-sig')
        print(f"\n💾 港股通完整分析结果已保存到: {hsgt_csv_path}")
        print(f"   (包含所有 {len(hsgt_analysis)} 家公司的详细数据)")
    else:
        print("未找到港股通持股比例变化数据")

    print("="*80)


# --- Script Entry Point ---
if __name__ == "__main__":
    # Define parameters for the strategy
    strategy_params = {
        'roc_std_window': 500,
        'lookback_days': LOOKBACK_DAYS
    }

    # Define the data path - 使用港股数据
    market_data_parquet_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')

    find_potential_trades(market_data_parquet_path, strategy_params)