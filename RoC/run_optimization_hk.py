#!/usr/bin/env python3
"""
Hong Kong RoC Strategy Optimization Runner

This script provides an easy interface to run parameter optimization for the Hong Kong RoC strategy.
It includes preset configurations for different optimization scenarios.

Usage:
    python run_optimization_hk.py [--preset quick|balanced|thorough]
"""

import sys
import os
import argparse
import subprocess
from datetime import datetime

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def get_optimization_presets():
    """Define optimization presets for different scenarios."""
    return {
        'quick': {
            'hours': 2.0,
            'description': 'Quick optimization for testing (2 hours)',
            'estimated_time': '~2 hours',
            'combinations': '~60 parameter combinations'
        },
        'balanced': {
            'hours': 7.0,
            'description': 'Balanced optimization with good coverage (7 hours)',
            'estimated_time': '~7 hours',
            'combinations': '~210 parameter combinations'
        },
        'thorough': {
            'hours': 12.0,
            'description': 'Thorough optimization for best results (12 hours)',
            'estimated_time': '~12 hours',
            'combinations': '~360 parameter combinations'
        }
    }

def show_presets():
    """Display available optimization presets."""
    presets = get_optimization_presets()
    
    print("Available Hong Kong Optimization Presets:")
    print("-" * 50)
    
    for name, config in presets.items():
        print(f"{name.upper()}:")
        print(f"  Duration: {config['hours']} hours")
        print(f"  Description: {config['description']}")
        print(f"  Estimated Time: {config['estimated_time']}")
        print(f"  Coverage: {config['combinations']}")
        print()

def get_user_confirmation(preset_config):
    """Get user confirmation for the selected preset."""
    print(f"\nSelected Configuration:")
    print(f"  Duration: {preset_config['hours']} hours")
    print(f"  Description: {preset_config['description']}")
    print(f"  Estimated Time: {preset_config['estimated_time']}")
    print(f"  Coverage: {preset_config['combinations']}")
    
    response = input(f"\nProceed with this configuration? (y/n): ")
    return response.lower() == 'y'

def custom_optimization():
    """Get custom optimization parameters from user."""
    print("\nCustom Optimization Configuration:")
    print("-" * 40)
    
    try:
        hours = float(input("Enter target optimization time in hours (1-24): "))
        if not 1 <= hours <= 24:
            print("Invalid hours. Must be between 1 and 24.")
            return None, None
        
        estimated_combinations = int(hours * 30)  # Rough estimate
        print(f"Estimated combinations to test: ~{estimated_combinations}")
        
        confirm = input(f"Proceed with {hours} hour optimization? (y/n): ")
        if confirm.lower() != 'y':
            return None, None
            
        return hours
        
    except ValueError:
        print("Invalid input. Please enter a valid number.")
        return None

def run_optimization(hours):
    """Run the Hong Kong optimization with specified parameters."""
    script_path = os.path.join(os.path.dirname(__file__), 'optimize_7h_hk.py')
    
    if not os.path.exists(script_path):
        print(f"❌ Optimization script not found: {script_path}")
        return False
    
    cmd = [
        sys.executable, script_path,
        '--hours', str(hours)
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"❌ Optimization failed with return code {e.returncode}")
        return False
    except Exception as e:
        print(f"❌ Error running optimization: {e}")
        return False

def check_prerequisites():
    """Check if required files exist."""
    required_files = [
        'run_full_market_backtest_RoC_HK.py',
        'run_portfolio_backtest_RoC_HK.py'
    ]
    
    missing_files = []
    for file_name in required_files:
        file_path = os.path.join(os.path.dirname(__file__), file_name)
        if not os.path.exists(file_path):
            missing_files.append(file_name)
    
    if missing_files:
        print("❌ Missing required files:")
        for file_name in missing_files:
            print(f"   - {file_name}")
        print("\nPlease ensure all Hong Kong strategy files are present.")
        return False
    
    # Check data files
    data_files = [
        os.path.join(project_root, 'data', 'h_shares_daily.parquet')
    ]
    
    missing_data = []
    for file_path in data_files:
        if not os.path.exists(file_path):
            missing_data.append(os.path.basename(file_path))
    
    if missing_data:
        print("⚠️  Missing data files:")
        for file_name in missing_data:
            print(f"   - {file_name}")
        print("\nOptimization may fail without proper data files.")
        
        response = input("Continue anyway? (y/n): ")
        if response.lower() != 'y':
            return False
    
    return True

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description='Hong Kong RoC Strategy Parameter Optimization',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_optimization_hk.py --preset quick     # 2-hour quick optimization
  python run_optimization_hk.py --preset balanced  # 7-hour balanced optimization
  python run_optimization_hk.py --preset thorough  # 12-hour thorough optimization
  python run_optimization_hk.py                    # Interactive mode
        """
    )
    
    parser.add_argument(
        '--preset', 
        choices=['quick', 'balanced', 'thorough', 'custom'],
        help='Use a predefined optimization preset'
    )
    
    args = parser.parse_args()
    
    print("="*80)
    print("🚀 HONG KONG ROC STRATEGY PARAMETER OPTIMIZATION")
    print("="*80)
    print("Objective: Maximize Sharpe ratio on in-sample Hong Kong data (2010-2023)")
    print("Market: Hong Kong H-Shares")
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed. Exiting.")
        return
    
    presets = get_optimization_presets()
    
    # Determine optimization parameters
    if args.preset:
        if args.preset == 'custom':
            hours = custom_optimization()
            if hours is None:
                print("Custom optimization cancelled.")
                return
        else:
            preset_config = presets[args.preset]
            hours = preset_config['hours']
            
            if not get_user_confirmation(preset_config):
                print("Optimization cancelled.")
                return
    else:
        # Interactive mode - show presets and let user choose
        show_presets()
        
        while True:
            choice = input("Select preset (quick/balanced/thorough/custom) or 'q' to quit: ").lower()
            
            if choice == 'q':
                print("Optimization cancelled.")
                return
            elif choice in presets:
                preset_config = presets[choice]
                if get_user_confirmation(preset_config):
                    hours = preset_config['hours']
                    break
            elif choice == 'custom':
                hours = custom_optimization()
                if hours is not None:
                    break
            else:
                print("Invalid choice. Please try again.")
    
    # Run optimization
    print(f"\nStarting Hong Kong optimization at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    success = run_optimization(hours)
    
    if success:
        print(f"\nOptimization completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\nNext steps:")
        print("1. Review the optimization results in RoC/backtest_results/")
        print("2. Run update_optimized_parameters_hk.py to update the strategy parameters")
        print("3. Test the updated Hong Kong strategy")
        
        print("\nCommands to run:")
        print("python update_optimized_parameters_hk.py")
        print("python run_full_market_backtest_RoC_HK.py")
        print("python run_portfolio_backtest_RoC_HK.py")
    else:
        print("\nOptimization failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
