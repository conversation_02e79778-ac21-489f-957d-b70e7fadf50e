import pandas as pd
import numpy as np
import os
import sys
import random
from multiprocessing import Pool, cpu_count
from functools import partial
from tqdm import tqdm

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from utilities.utils_indicators import rate_of_change, average_true_range

def apply_roc_atr_strategy_hk(df_ohlc: pd.DataFrame,
                              roc_period: int = 30,
                              roc_std_window: int = 750,
                              roc_std_multiplier: float = 1.75,
                              atr_period: int = 14,
                              profit_target_atr_multiplier: float = 1.75,
                              stop_loss_atr_multiplier: float = 1.5,
                              max_hold_days: int = 30):
    """
    港股版本的ROC/ATR策略
    """
    
    df = df_ohlc.copy()
    
    # 确保数据足够长
    if len(df) < max(roc_std_window, 100):
        return df, []
    
    # 指标计算
    roc = rate_of_change(df['Close'], period=roc_period)
    atr = average_true_range(df['High'], df['Low'], df['Close'], period=atr_period)
    roc_std = roc.rolling(window=roc_std_window).std()
    adaptive_threshold = -(roc_std * roc_std_multiplier)

    # 入场信号生成
    entry_condition = (roc < adaptive_threshold) & (roc.shift(1) < roc)
    df['entry_signal'] = entry_condition

    # 交易处理
    trades_log = []
    last_exit_date = None
    
    entry_dates = df[df['entry_signal']].index
    
    for entry_date in entry_dates:
        if last_exit_date is not None and entry_date <= last_exit_date:
            continue
            
        entry_price = df.loc[entry_date, 'Open']
        entry_atr = atr.loc[entry_date]
        
        if pd.isna(entry_atr) or entry_atr <= 0:
            continue
            
        profit_target = entry_price + (entry_atr * profit_target_atr_multiplier)
        stop_loss = entry_price - (entry_atr * stop_loss_atr_multiplier)
        max_exit_date = entry_date + pd.Timedelta(days=max_hold_days)
        
        exit_date = None
        exit_price = None
        exit_reason = None
        
        future_dates = df.index[df.index > entry_date]
        
        for check_date in future_dates:
            if check_date > max_exit_date:
                exit_date = check_date
                exit_price = df.loc[check_date, 'Open']
                exit_reason = 'Max Hold Time'
                break
                
            high_price = df.loc[check_date, 'High']
            low_price = df.loc[check_date, 'Low']
            
            if high_price >= profit_target:
                exit_date = check_date
                exit_price = profit_target
                exit_reason = 'Profit Target'
                break
            elif low_price <= stop_loss:
                exit_date = check_date
                exit_price = stop_loss
                exit_reason = 'Stop Loss'
                break
        
        if exit_date is None and len(future_dates) > 0:
            exit_date = future_dates[-1]
            exit_price = df.loc[exit_date, 'Close']
            exit_reason = 'End of Data'
        
        if exit_date is not None and exit_price is not None:
            pnl_absolute = exit_price - entry_price
            pnl_pct = (pnl_absolute / entry_price) * 100
            hold_days = (exit_date - entry_date).days
            
            trade_record = {
                'EntryDate': entry_date,
                'ExitDate': exit_date,
                'EntryPrice': entry_price,
                'ExitPrice': exit_price,
                'PnL_absolute': pnl_absolute,
                'PnL_pct': pnl_pct,
                'HoldDays': hold_days,
                'ExitReason': exit_reason,
                'ATR_at_entry': entry_atr,
                'ProfitTarget': profit_target,
                'StopLoss': stop_loss
            }
            
            trades_log.append(trade_record)
            last_exit_date = exit_date

    return df, trades_log


def apply_random_entry_exit_strategy_hk(df_ohlc: pd.DataFrame,
                                        num_trades: int = None,
                                        min_hold_days: int = 1,
                                        max_hold_days: int = 30,
                                        seed: int = None):
    """
    港股版本的随机进出场策略，用于基准对比

    Args:
        df_ohlc: OHLC DataFrame
        num_trades: 随机交易数量 (如果为None，使用与真实策略相同的数量)
        min_hold_days: 最小持有天数
        max_hold_days: 最大持有天数
        seed: 随机种子，用于结果可重现

    Returns:
        list: 随机交易记录列表
    """
    if seed is not None:
        np.random.seed(seed)

    df = df_ohlc.copy()
    trades_log = []

    # 如果未指定交易数量，根据数据长度估算
    if num_trades is None:
        num_trades = max(1, len(df) // 100)

    # 生成随机入场日期
    available_dates = df.index[:-max_hold_days]  # 确保有足够的持有期间
    if len(available_dates) < num_trades:
        num_trades = len(available_dates)

    random_entry_dates = np.random.choice(available_dates, size=num_trades, replace=False)
    random_entry_dates = sorted(random_entry_dates)

    for entry_date in random_entry_dates:
        try:
            entry_price = df.loc[entry_date, 'Close']

            # 随机持有期间
            hold_days = np.random.randint(min_hold_days, max_hold_days + 1)

            # 找到出场日期
            entry_idx = df.index.get_loc(entry_date)
            if entry_idx + hold_days < len(df):
                exit_date = df.index[entry_idx + hold_days]
            else:
                exit_date = df.index[-1]

            exit_price = df.loc[exit_date, 'Close']

            pnl_absolute = exit_price - entry_price
            pnl_percent = (pnl_absolute / entry_price) * 100 if entry_price != 0 else 0

            trades_log.append({
                'EntryDate': entry_date,
                'ExitDate': exit_date,
                'EntryPrice': entry_price,
                'ExitPrice': exit_price,
                'PnL_absolute': pnl_absolute,
                'PnL_pct': pnl_percent,
                'HoldDays': (exit_date - entry_date).days,
                'ExitReason': 'Random Exit'
            })

        except Exception:
            continue

    return trades_log


def run_hk_single_stock_test():
    """运行港股单股测试"""
    print("=== 港股单股测试 ===")
    
    hk_data_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')
    
    if not os.path.exists(hk_data_path):
        print(f"❌ 港股数据文件不存在: {hk_data_path}")
        return
    
    df_hk = pd.read_parquet(hk_data_path)
    
    # 数据格式转换
    df_hk = df_hk.rename(columns={
        'time_key': 'Date',
        'stock_code': 'StockCode',
        'open': 'Open',
        'high': 'High',
        'low': 'Low',
        'close': 'Close',
        'volume': 'Volume'
    })
    
    df_hk['Date'] = pd.to_datetime(df_hk['Date'])
    
    # 选择数据最多的股票进行测试
    stock_counts = df_hk['StockCode'].value_counts()
    test_stock = stock_counts.index[0]
    
    print(f"📊 测试股票: HK.{test_stock}")
    
    stock_data = df_hk[df_hk['StockCode'] == test_stock].copy()
    stock_data = stock_data.set_index('Date').sort_index()
    
    print(f"📊 数据范围: {stock_data.index[0]} 到 {stock_data.index[-1]}")
    print(f"📊 数据长度: {len(stock_data)} 天")
    
    df_result, trades = apply_roc_atr_strategy_hk(stock_data)
    
    if not trades:
        print("❌ 没有找到交易信号")
        return
    
    trades_df = pd.DataFrame(trades)
    print(f"\n✅ 找到 {len(trades)} 笔交易")
    print(f"📊 胜率: {(trades_df['PnL_pct'] > 0).mean() * 100:.2f}%")
    print(f"📊 平均收益: {trades_df['PnL_pct'].mean():.2f}%")
    print(f"📊 平均持有天数: {trades_df['HoldDays'].mean():.1f} 天")
    
    print(f"\n前5笔交易详情:")
    print(trades_df[['EntryDate', 'ExitDate', 'EntryPrice', 'ExitPrice', 'PnL_pct', 'ExitReason']].head())

    return trades_df, stock_data


def run_hk_single_stock_random_test(df_sample, trades):
    """对港股单股进行随机基准对比测试"""
    if not trades:
        print("\n🎲 随机基准测试: 无法进行（策略无交易记录）")
        return

    print(f"\n{'='*60}")
    print("🎲 港股随机进出场基准测试")
    print(f"{'='*60}")

    # 生成与策略相同数量的随机交易
    random_trades = apply_random_entry_exit_strategy_hk(
        df_sample,
        num_trades=len(trades),
        seed=42
    )

    if not random_trades:
        print("❌ 无法生成随机交易")
        return

    # 计算随机交易表现
    random_trades_df = pd.DataFrame(random_trades)
    investment_amount = 10000  # 每笔交易1万港币
    random_trades_df['Shares_Bought'] = investment_amount / random_trades_df['EntryPrice']
    random_trades_df['PnL_10k'] = random_trades_df['Shares_Bought'] * random_trades_df['PnL_absolute']

    random_winning_trades = random_trades_df[random_trades_df['PnL_pct'] > 0]
    random_win_rate = (len(random_winning_trades) / len(random_trades_df)) * 100
    random_total_pnl_10k = random_trades_df['PnL_10k'].sum()
    random_avg_pnl_pct = random_trades_df['PnL_pct'].mean()
    random_avg_pnl_10k = random_trades_df['PnL_10k'].mean()

    # 策略表现（用于对比）
    strategy_trades_df = pd.DataFrame(trades)
    strategy_trades_df['Shares_Bought'] = investment_amount / strategy_trades_df['EntryPrice']
    strategy_trades_df['PnL_10k'] = strategy_trades_df['Shares_Bought'] * strategy_trades_df['PnL_absolute']
    strategy_total_pnl_10k = strategy_trades_df['PnL_10k'].sum()
    strategy_win_rate = (len(strategy_trades_df[strategy_trades_df['PnL_pct'] > 0]) / len(strategy_trades_df)) * 100

    print(f"📊 对比结果:")
    print(f"   交易数量: {len(trades)} (相同)")
    print(f"   策略总收益: HK${strategy_total_pnl_10k:.2f}")
    print(f"   随机总收益: HK${random_total_pnl_10k:.2f}")
    print(f"   策略优势: HK${strategy_total_pnl_10k - random_total_pnl_10k:.2f}")
    print(f"   策略胜率: {strategy_win_rate:.1f}% vs 随机胜率: {random_win_rate:.1f}%")

    # 计算Alpha
    alpha_pct = ((strategy_total_pnl_10k - random_total_pnl_10k) / (len(trades) * investment_amount)) * 100
    print(f"   策略Alpha: {alpha_pct:.2f}% (超越随机基准的收益)")

    if random_total_pnl_10k != 0:
        outperformance_ratio = strategy_total_pnl_10k / random_total_pnl_10k
        print(f"   表现倍数: {outperformance_ratio:.2f}x")

    print(f"\n📋 随机交易详情:")
    print(f"   平均收益: {random_avg_pnl_pct:.2f}% (HK${random_avg_pnl_10k:.2f})")
    print(f"   最佳交易: {random_trades_df['PnL_pct'].max():.2f}%")
    print(f"   最差交易: {random_trades_df['PnL_pct'].min():.2f}%")


def test_single_hk_stock(stock_code, df_stock, strategy_params, include_random_test=False):
    """测试单只港股的策略表现"""
    try:
        # 应用真实策略
        _, trades = apply_roc_atr_strategy_hk(df_stock, **strategy_params)

        result = {
            'stock_code': stock_code,
            'num_trades': 0,
            'total_pnl_pct': 0,
            'total_pnl_10k': 0,
            'win_rate': 0,
            'avg_pnl_pct': 0,
            'avg_pnl_10k': 0,
            'error': None
        }

        if trades:
            # 计算真实策略表现指标
            trades_df = pd.DataFrame(trades)
            investment_amount = 10000  # 每笔交易1万港币
            trades_df['Shares_Bought'] = investment_amount / trades_df['EntryPrice']
            trades_df['PnL_10k'] = trades_df['Shares_Bought'] * trades_df['PnL_absolute']

            winning_trades = trades_df[trades_df['PnL_pct'] > 0]
            win_rate = (len(winning_trades) / len(trades_df)) * 100 if len(trades_df) > 0 else 0

            result.update({
                'num_trades': len(trades_df),
                'total_pnl_pct': trades_df['PnL_pct'].sum(),
                'total_pnl_10k': trades_df['PnL_10k'].sum(),
                'win_rate': win_rate,
                'avg_pnl_pct': trades_df['PnL_pct'].mean(),
                'avg_pnl_10k': trades_df['PnL_10k'].mean(),
            })

            # 添加随机基准测试（如果需要）
            if include_random_test and len(trades_df) > 0:
                random_trades = apply_random_entry_exit_strategy_hk(
                    df_stock,
                    num_trades=len(trades_df),
                    seed=42  # 固定种子确保结果可重现
                )

                if random_trades:
                    random_trades_df = pd.DataFrame(random_trades)
                    random_trades_df['Shares_Bought'] = investment_amount / random_trades_df['EntryPrice']
                    random_trades_df['PnL_10k'] = random_trades_df['Shares_Bought'] * random_trades_df['PnL_absolute']

                    random_winning_trades = random_trades_df[random_trades_df['PnL_pct'] > 0]
                    random_win_rate = (len(random_winning_trades) / len(random_trades_df)) * 100 if len(random_trades_df) > 0 else 0

                    result.update({
                        'random_num_trades': len(random_trades_df),
                        'random_total_pnl_pct': random_trades_df['PnL_pct'].sum(),
                        'random_total_pnl_10k': random_trades_df['PnL_10k'].sum(),
                        'random_win_rate': random_win_rate,
                        'random_avg_pnl_pct': random_trades_df['PnL_pct'].mean(),
                        'random_avg_pnl_10k': random_trades_df['PnL_10k'].mean(),
                    })

        return result

    except Exception as e:
        return {
            'stock_code': stock_code,
            'error': str(e)
        }


def test_random_hk_stocks(num_stocks=50, strategy_params=None, include_random_benchmark=False):
    """
    对随机选择的港股进行策略测试

    Args:
        num_stocks (int): 要测试的股票数量
        strategy_params (dict): 策略参数
        include_random_benchmark (bool): 是否包含随机基准对比

    Returns:
        dict: 汇总统计结果
    """
    benchmark_text = " (含随机基准对比)" if include_random_benchmark else ""
    print(f"\n{'='*80}")
    print(f"🎯 港股ROC策略 - {num_stocks}只随机股票测试{benchmark_text}")
    print(f"{'='*80}")

    # 加载港股数据
    hk_data_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')

    if not os.path.exists(hk_data_path):
        print(f"❌ 港股数据文件不存在: {hk_data_path}")
        return None

    df_hk = pd.read_parquet(hk_data_path)

    # 数据格式转换
    df_hk = df_hk.rename(columns={
        'time_key': 'Date',
        'stock_code': 'StockCode',
        'open': 'Open',
        'high': 'High',
        'low': 'Low',
        'close': 'Close',
        'volume': 'Volume'
    })

    df_hk['Date'] = pd.to_datetime(df_hk['Date'])

    # 获取所有可用的股票代码
    all_stocks = df_hk['StockCode'].unique()
    print(f"📊 发现 {len(all_stocks)} 只港股")

    # 随机选择股票
    if len(all_stocks) < num_stocks:
        print(f"⚠️  只有 {len(all_stocks)} 只股票可用，使用全部股票")
        selected_stocks = all_stocks
    else:
        selected_stocks = random.sample(list(all_stocks), num_stocks)

    print(f"🎲 选择了 {len(selected_stocks)} 只股票进行测试")

    # 使用默认参数（如果未提供）
    if strategy_params is None:
        strategy_params = {
            'roc_period': 30,
            'roc_std_window': 750,
            'roc_std_multiplier': 2.0,
            'atr_period': 14,
            'profit_target_atr_multiplier': 2.0,
            'stop_loss_atr_multiplier': 1.5,
            'max_hold_days': 30
        }

    print(f"🚀 开始处理股票...")

    results = []
    for i, stock_code in enumerate(selected_stocks, 1):
        try:
            print(f"处理 {i}/{len(selected_stocks)}: HK.{stock_code}", end=" ... ")

            # 获取单只股票数据
            stock_data = df_hk[df_hk['StockCode'] == stock_code].copy()
            stock_data = stock_data.set_index('Date').sort_index()

            # 确保有足够的数据
            if len(stock_data) < strategy_params.get('roc_std_window', 750):
                print("数据不足")
                continue

            result = test_single_hk_stock(
                stock_code,
                stock_data,
                strategy_params,
                include_random_test=include_random_benchmark
            )

            if result and result.get('error') is None and result.get('num_trades', 0) > 0:
                results.append(result)
                print(f"完成 ({result['num_trades']}笔交易)")
            else:
                print("无交易或出错")

        except Exception as e:
            print(f"错误: {str(e)}")
            continue

    return results


def analyze_hk_results(results, include_random_benchmark=False):
    """分析港股测试结果"""
    if not results:
        print("❌ 没有成功的测试结果可供分析！")
        return None

    print(f"\n📈 港股测试结果汇总:")
    print(f"✅ 成功测试: {len(results)} 只股票")

    # 检查是否有随机基准数据
    has_random_data = any('random_num_trades' in r for r in results)

    # 计算综合统计
    total_trades = sum(r['num_trades'] for r in results)
    total_pnl_10k = sum(r['total_pnl_10k'] for r in results)

    # 随机基准统计（如果可用）
    if has_random_data:
        random_results = [r for r in results if 'random_num_trades' in r]
        total_random_trades = sum(r['random_num_trades'] for r in random_results)
        total_random_pnl_10k = sum(r['random_total_pnl_10k'] for r in random_results)

    # 计算整体胜率
    total_winning_trades = 0
    for result in results:
        num_trades = result['num_trades']
        win_rate = result['win_rate'] / 100
        winning_trades = int(num_trades * win_rate)
        total_winning_trades += winning_trades

    overall_win_rate = (total_winning_trades / total_trades) * 100 if total_trades > 0 else 0
    avg_pnl_per_trade_pct = np.mean([r['avg_pnl_pct'] for r in results])
    avg_pnl_per_trade_10k = total_pnl_10k / total_trades if total_trades > 0 else 0

    # 找到最佳和最差表现的股票
    best_stock = max(results, key=lambda x: x['total_pnl_pct'])
    worst_stock = min(results, key=lambda x: x['total_pnl_pct'])

    # 检查是否有亏损股票
    losing_stocks = [r for r in results if r['total_pnl_pct'] < 0]
    has_losing_stocks = len(losing_stocks) > 0

    print(f"\n🎯 综合统计 - {len(results)}只港股合计表现:")
    print(f"{'='*60}")
    print(f"📊 交易统计:")
    print(f"   总交易数: {total_trades}")
    print(f"   获利交易: {total_winning_trades} ({overall_win_rate:.1f}%)")
    print(f"   亏损交易: {total_trades - total_winning_trades} ({100-overall_win_rate:.1f}%)")

    print(f"\n💰 盈亏统计:")
    print(f"   合计利润 (1万港币/交易): HK${total_pnl_10k:,.2f}")
    print(f"   平均每笔收益: {avg_pnl_per_trade_pct:.2f}% (HK${avg_pnl_per_trade_10k:.2f})")

    print(f"\n📈 关键指标:")
    print(f"   胜率: {overall_win_rate:.2f}%")

    # 随机基准对比（如果可用）
    if has_random_data:
        random_avg_win_rate = np.mean([r['random_win_rate'] for r in random_results])
        random_avg_pnl_per_trade_10k = total_random_pnl_10k / total_random_trades if total_random_trades > 0 else 0

        print(f"\n🎲 随机基准对比:")
        print(f"   策略总收益: HK${total_pnl_10k:,.2f}")
        print(f"   随机总收益: HK${total_random_pnl_10k:,.2f}")
        print(f"   策略优势: HK${total_pnl_10k - total_random_pnl_10k:,.2f}")
        print(f"   策略胜率: {overall_win_rate:.2f}% vs 随机胜率: {random_avg_win_rate:.2f}%")

        # 计算Alpha
        alpha_pct = ((total_pnl_10k - total_random_pnl_10k) / (total_trades * 10000)) * 100
        print(f"   策略Alpha: {alpha_pct:.2f}% (超越随机基准的收益)")

        if total_random_pnl_10k != 0:
            outperformance_ratio = total_pnl_10k / total_random_pnl_10k
            print(f"   表现倍数: {outperformance_ratio:.2f}x (策略收益/随机收益)")

    print(f"\n🏆 最佳表现股票: HK.{best_stock['stock_code']}")
    print(f"   总收益: {best_stock['total_pnl_pct']:.2f}% (HK${best_stock['total_pnl_10k']:.2f})")
    print(f"   交易数: {best_stock['num_trades']}, 胜率: {best_stock['win_rate']:.1f}%")

    # 更智能的最差表现显示
    if has_losing_stocks:
        print(f"\n📉 最差表现股票: HK.{worst_stock['stock_code']}")
        print(f"   总收益: {worst_stock['total_pnl_pct']:.2f}% (HK${worst_stock['total_pnl_10k']:.2f})")
        print(f"   交易数: {worst_stock['num_trades']}, 胜率: {worst_stock['win_rate']:.1f}%")
    else:
        print(f"\n📊 最低收益股票: HK.{worst_stock['stock_code']} (所有股票均盈利)")
        print(f"   总收益: {worst_stock['total_pnl_pct']:.2f}% (HK${worst_stock['total_pnl_10k']:.2f})")
        print(f"   交易数: {worst_stock['num_trades']}, 胜率: {worst_stock['win_rate']:.1f}%")

    # 保存详细结果
    results_df = pd.DataFrame(results)
    output_dir = os.path.join(project_root, 'RoC', 'backtest_results')
    os.makedirs(output_dir, exist_ok=True)
    output_file = os.path.join(output_dir, f'hk_random_stocks_test_{len(results)}_stocks.csv')
    results_df.to_csv(output_file, index=False)
    print(f"\n💾 详细结果已保存至: {output_file}")

    return {
        'successful_stocks': len(results),
        'total_trades': total_trades,
        'total_pnl_10k': total_pnl_10k,
        'overall_win_rate': overall_win_rate,
        'avg_pnl_per_trade_pct': avg_pnl_per_trade_pct,
        'avg_pnl_per_trade_10k': avg_pnl_per_trade_10k,
        'best_stock': best_stock,
        'worst_stock': worst_stock,
        'detailed_results': results
    }


def ask_user_for_hk_multi_stock_test():
    """
    询问用户是否要进行港股多公司回测
    返回: (是否运行, 股票数量, 是否包含随机基准)
    """
    try:
        print(f"\n{'='*80}")
        print("🎯 港股单股测试已完成！")
        print(f"{'='*80}")

        # 询问是否要进行多公司回测
        while True:
            choice = input("\n是否要进行港股多公司随机回测？(y/n，默认y): ").strip().lower()
            if choice in ['y', 'yes', '']:  # 默认为yes
                break
            elif choice in ['n', 'no']:
                print("跳过多公司回测")
                return False, 0, False
            else:
                print("请输入 y 或 n")

        # 询问测试多少只股票
        while True:
            try:
                num_input = input("请输入要测试的港股数量 (默认50): ").strip()
                if num_input == '':
                    num_stocks = 50
                else:
                    num_stocks = int(num_input)
                    if num_stocks <= 0:
                        print("股票数量必须大于0，请重新输入")
                        continue
                break
            except ValueError:
                print("请输入有效的数字")

        # 询问是否要进行随机基准对比
        while True:
            random_choice = input("\n是否要进行随机进出场基准对比？(y/n，默认n): ").strip().lower()
            if random_choice in ['y', 'yes']:
                include_random = True
                print("✅ 将包含随机基准对比测试")
                break
            elif random_choice in ['n', 'no', '']:  # 默认为no
                include_random = False
                break
            else:
                print("请输入 y 或 n")

        print(f"✅ 将测试 {num_stocks} 只随机选择的港股")
        return True, num_stocks, include_random

    except KeyboardInterrupt:
        print("\n\n用户取消操作")
        return False, 0, False
    except Exception as e:
        print(f"输入错误: {e}")
        return False, 0, False


if __name__ == "__main__":
    import sys

    # 检查是否只运行随机股票测试
    if len(sys.argv) > 1 and sys.argv[1] == '--random-only':
        num_stocks = int(sys.argv[2]) if len(sys.argv) > 2 else 50
        print(f"运行港股随机股票测试，测试 {num_stocks} 只股票...")
        results = test_random_hk_stocks(num_stocks)
        if results:
            analyze_hk_results(results)
    else:
        # 首先运行单股测试
        trades_df, stock_data = run_hk_single_stock_test()

        if trades_df is not None:
            # 添加随机基准测试
            run_hk_single_stock_random_test(stock_data, trades_df.to_dict('records'))

            # 询问用户是否要运行多股测试
            run_multi_test, num_stocks, include_random = ask_user_for_hk_multi_stock_test()

            if run_multi_test:
                print(f"\n{'='*80}")
                benchmark_text = " (含随机基准对比)" if include_random else ""
                print(f"🎲 开始测试 {num_stocks} 只随机选择的港股{benchmark_text}...")
                print(f"{'='*80}")
                results = test_random_hk_stocks(num_stocks, include_random_benchmark=include_random)
                if results:
                    analyze_hk_results(results, include_random_benchmark=include_random)
            else:
                print("\n✅ 港股测试完成！")
        else:
            print("\n❌ 单股测试失败，跳过后续测试")