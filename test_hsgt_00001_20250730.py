#!/usr/bin/env python3
"""
测试单独下载00001(长和)在2025-07-30的港股通数据

这个脚本专门用于测试港股通数据获取功能，特别是针对2025年7月30日的数据缺失问题。
"""

import pandas as pd
import time
from datetime import datetime, timedelta
import sys
import os

# 尝试导入AKShare
try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
    print("✅ AKShare imported successfully")
except ImportError:
    AKSHARE_AVAILABLE = False
    print("❌ AKShare not available, cannot test HSGT data")
    sys.exit(1)

def test_hsgt_data_for_00001():
    """测试获取00001的港股通数据"""
    stock_code = "00001"
    stock_name = "长和"
    
    print(f"🔍 Testing HSGT data for {stock_code} ({stock_name})")
    print("=" * 60)
    
    try:
        print("📡 Fetching HSGT data from AKShare...")
        start_time = time.time()
        
        # 获取港股通数据
        hsgt_data = ak.stock_hsgt_individual_em(symbol=stock_code)
        
        fetch_time = time.time() - start_time
        print(f"⏱️  Data fetch completed in {fetch_time:.2f} seconds")
        
        if hsgt_data is None or hsgt_data.empty:
            print("❌ No HSGT data returned")
            return None
        
        print(f"📊 Retrieved {len(hsgt_data)} HSGT records")
        
        # 显示数据结构
        print("\n📋 Data columns:")
        for i, col in enumerate(hsgt_data.columns):
            print(f"  {i+1:2d}. {col}")
        
        # 显示最新几条记录
        print(f"\n📅 Latest 10 HSGT records:")
        print("-" * 80)
        
        # 按日期排序
        hsgt_data_sorted = hsgt_data.sort_values('持股日期', ascending=False)
        
        for i, (_, row) in enumerate(hsgt_data_sorted.head(10).iterrows()):
            date_str = row['持股日期']
            close_price = row['当日收盘价']
            holding_shares = row['持股数量']
            holding_value = row['持股市值']
            holding_ratio = row['持股数量占A股百分比']
            
            print(f"{i+1:2d}. {date_str} | 收盘价: {close_price:>8.2f} | "
                  f"持股: {holding_shares:>12,.0f} | 市值: {holding_value:>15,.0f} | "
                  f"比例: {holding_ratio:>5.2f}%")
        
        # 检查2025-07-30的数据
        print(f"\n🎯 Checking for 2025-07-30 data:")
        print("-" * 40)

        target_date = "2025-07-30"

        # 检查不同的日期格式
        target_data_exact = hsgt_data[hsgt_data['持股日期'] == target_date]
        target_data_contains = hsgt_data[hsgt_data['持股日期'].astype(str).str.contains(target_date, na=False)]

        print(f"Searching for: '{target_date}'")
        print(f"Exact match results: {len(target_data_exact)}")
        print(f"Contains match results: {len(target_data_contains)}")

        # 显示实际的日期格式
        sample_dates = hsgt_data['持股日期'].head(3).tolist()
        print(f"Sample date formats in data: {sample_dates}")
        print(f"Sample date types: {[type(d) for d in sample_dates]}")

        if not target_data_exact.empty:
            print(f"✅ Found exact match for {target_date}!")
            row = target_data_exact.iloc[0]
            print(f"   收盘价: {row['当日收盘价']}")
            print(f"   持股数量: {row['持股数量']:,.0f}")
            print(f"   持股市值: {row['持股市值']:,.0f}")
            print(f"   持股比例: {row['持股数量占A股百分比']:.2f}%")
        elif not target_data_contains.empty:
            print(f"✅ Found partial match for {target_date}!")
            row = target_data_contains.iloc[0]
            print(f"   实际日期: {row['持股日期']}")
            print(f"   收盘价: {row['当日收盘价']}")
            print(f"   持股数量: {row['持股数量']:,.0f}")
            print(f"   持股市值: {row['持股市值']:,.0f}")
            print(f"   持股比例: {row['持股数量占A股百分比']:.2f}%")
        else:
            print(f"❌ No data found for {target_date}")

            # 查找最接近的日期
            hsgt_data['date_obj'] = pd.to_datetime(hsgt_data['持股日期'])
            target_date_obj = pd.to_datetime(target_date)

            # 找到最接近的日期
            hsgt_data['date_diff'] = abs(hsgt_data['date_obj'] - target_date_obj)
            closest_data = hsgt_data.loc[hsgt_data['date_diff'].idxmin()]

            print(f"   最接近的数据日期: {closest_data['持股日期']}")
            print(f"   距离目标日期: {closest_data['date_diff'].days} 天")
        
        # 检查最新数据日期
        latest_date = hsgt_data_sorted.iloc[0]['持股日期']
        print(f"\n📈 Latest HSGT data date: {latest_date}")
        
        # 检查数据连续性
        print(f"\n🔗 Data continuity check:")
        print("-" * 30)
        
        recent_dates = hsgt_data_sorted.head(5)['持股日期'].tolist()
        print("Recent 5 dates:")
        for i, date in enumerate(recent_dates):
            print(f"  {i+1}. {date}")
        
        # 检查是否有数据缺口
        hsgt_data['date_obj'] = pd.to_datetime(hsgt_data['持股日期'])
        date_range = pd.date_range(start=hsgt_data['date_obj'].min(), 
                                 end=hsgt_data['date_obj'].max(), 
                                 freq='D')
        
        missing_dates = []
        for date in date_range:
            date_str = date.strftime('%Y-%m-%d')
            if date_str not in hsgt_data['持股日期'].values:
                # 只检查工作日（周一到周五）
                if date.weekday() < 5:  # 0=Monday, 4=Friday
                    missing_dates.append(date_str)
        
        if missing_dates:
            print(f"\n⚠️  Found {len(missing_dates)} missing weekdays in HSGT data")
            if len(missing_dates) <= 10:
                print("Missing dates:")
                for date in missing_dates[-10:]:  # 显示最近的10个缺失日期
                    print(f"  - {date}")
            else:
                print(f"Recent missing dates (last 10):")
                for date in missing_dates[-10:]:
                    print(f"  - {date}")
        else:
            print("✅ No missing weekdays found in HSGT data")
        
        return hsgt_data
        
    except Exception as e:
        print(f"❌ Error fetching HSGT data: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        print("Full traceback:")
        traceback.print_exc()
        return None

def compare_with_existing_data():
    """比较新获取的数据与现有文件中的数据"""
    print(f"\n🔄 Comparing with existing data...")
    print("=" * 40)
    
    existing_file = "/Users/<USER>/AI/Cursor/Futu/data/H_daily/00001.csv"
    
    if not os.path.exists(existing_file):
        print(f"❌ Existing file not found: {existing_file}")
        return
    
    try:
        # 读取现有数据
        existing_df = pd.read_csv(existing_file)
        
        # 检查港股通数据
        hsgt_data_existing = existing_df[existing_df['hsgt_date'].notna()]
        
        print(f"📁 Existing file: {len(existing_df)} total records")
        print(f"📊 Existing HSGT records: {len(hsgt_data_existing)}")
        
        if len(hsgt_data_existing) > 0:
            latest_hsgt_date = hsgt_data_existing['hsgt_date'].max()
            print(f"📅 Latest HSGT date in file: {latest_hsgt_date}")
            
            # 检查2025-07-30
            target_data = existing_df[existing_df['hsgt_date'] == '2025-07-30']
            if not target_data.empty:
                print(f"✅ File contains 2025-07-30 HSGT data")
            else:
                print(f"❌ File missing 2025-07-30 HSGT data")
                
                # 检查最近几天的数据
                print(f"\nRecent HSGT data in file:")
                recent_hsgt = hsgt_data_existing.tail(5)
                for _, row in recent_hsgt.iterrows():
                    print(f"  {row['hsgt_date']}: 持股 {row['hsgt_holding_shares']:,.0f}")
        else:
            print(f"❌ No HSGT data found in existing file")
            
    except Exception as e:
        print(f"❌ Error reading existing file: {e}")

def test_akshare_api_status():
    """测试AKShare API的状态"""
    print(f"\n🔧 Testing AKShare API status...")
    print("=" * 35)
    
    try:
        # 测试一个简单的API调用
        print("📡 Testing basic AKShare functionality...")
        
        # 获取港股通成分股
        print("🔍 Fetching HSGT constituent stocks...")
        hsgt_stocks = ak.stock_hsgt_stock_statistics_em(symbol="北向持股", period="今日")
        
        if hsgt_stocks is not None and not hsgt_stocks.empty:
            print(f"✅ AKShare API working - got {len(hsgt_stocks)} stocks")
            
            # 检查00001是否在列表中
            if '00001' in hsgt_stocks['代码'].values:
                print("✅ 00001 (长和) found in HSGT constituent stocks")
            else:
                print("⚠️  00001 (长和) not found in current HSGT constituent stocks")
                print("This might explain why recent HSGT data is missing")
        else:
            print("❌ AKShare API returned empty data")
            
    except Exception as e:
        print(f"❌ AKShare API test failed: {e}")

def main():
    """主函数"""
    print("🧪 HSGT Data Test for 00001 (长和) - 2025-07-30")
    print("=" * 60)
    print(f"Test time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试AKShare API状态
    test_akshare_api_status()
    
    # 测试获取港股通数据
    hsgt_data = test_hsgt_data_for_00001()
    
    # 比较现有数据
    compare_with_existing_data()
    
    print(f"\n🏁 Test completed!")
    print("=" * 20)
    
    if hsgt_data is not None:
        print("✅ HSGT data fetch successful")
        
        # 检查2025-07-30数据
        target_data = hsgt_data[hsgt_data['持股日期'] == '2025-07-30']
        if not target_data.empty:
            print("✅ 2025-07-30 HSGT data is available from API")
            print("💡 Suggestion: Re-run the daily data update script to get latest data")
        else:
            print("❌ 2025-07-30 HSGT data not available from API")
            print("💡 This confirms the data source doesn't have this date yet")
    else:
        print("❌ HSGT data fetch failed")
        print("💡 Check internet connection and AKShare API status")

if __name__ == "__main__":
    main()
