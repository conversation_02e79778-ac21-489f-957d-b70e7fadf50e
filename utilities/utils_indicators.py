# utils_indicators.py

import pandas as pd
import numpy as np

def rate_of_change(close_prices: pd.Series, period: int = 14) -> pd.Series:
    """
    Calculate Rate of Change (ROC).
    ROC = [(Close - Close_n_periods_ago) / Close_n_periods_ago] * 100

    Args:
        close_prices (pd.Series): Series of closing prices.
        period (int): The period for ROC calculation. Default is 14.

    Returns:
        pd.Series: Series containing the ROC values.
    """
    if not isinstance(close_prices, pd.Series):
        raise TypeError("close_prices must be a pandas Series.")
    if not isinstance(period, int) or period <= 0:
        raise ValueError("period must be a positive integer.")
    if len(close_prices) <= period:
        # Not enough data to calculate ROC for the given period.
        # pct_change will handle this by returning NaNs for initial values.
        pass

    # Using pct_change, which is (current - previous) / previous
    roc = close_prices.pct_change(periods=period) * 100
    roc.name = f'ROC_{period}'
    return roc

def relative_strength_index(close_prices: pd.Series, period: int = 14) -> pd.Series:
    """
    Calculate Relative Strength Index (RSI).
    RSI = 100 - (100 / (1 + RS))
    RS = Average Gain / Average Loss (using Wilder's smoothing)

    Args:
        close_prices (pd.Series): Series of closing prices.
        period (int): The period for RSI calculation. Default is 14.

    Returns:
        pd.Series: Series containing the RSI values.
    """
    if not isinstance(close_prices, pd.Series):
        raise TypeError("close_prices must be a pandas Series.")
    if not isinstance(period, int) or period <= 0:
        raise ValueError("period must be a positive integer.")
    if len(close_prices) <= period: # Need at least period+1 data points for the first diff
        return pd.Series(index=close_prices.index, dtype=float, name=f'RSI_{period}')

    delta = close_prices.diff(1)  # Price change from one period to the next

    gain = delta.where(delta > 0, 0.0)
    loss = -delta.where(delta < 0, 0.0) # Ensure loss is positive

    # Use Wilder's smoothing (equivalent to EMA with alpha = 1/period).
    # pandas ewm with com=period-1 is equivalent to Wilder's smoothing.
    avg_gain = gain.ewm(com=period - 1, min_periods=period).mean()
    avg_loss = loss.ewm(com=period - 1, min_periods=period).mean()

    # Calculate RS, handling division by zero for avg_loss.
    rs = avg_gain / avg_loss
    
    # Calculate RSI. If avg_loss is 0, RSI is 100 (strong uptrend).
    # This also handles the case where both avg_gain and avg_loss are 0 (no price change), setting RSI to 100.
    rsi_values = np.where(avg_loss == 0, 100.0, 100.0 - (100.0 / (1.0 + rs)))
    
    rsi = pd.Series(rsi_values, index=close_prices.index, name=f'RSI_{period}')
    
    return rsi

def average_true_range(high_prices: pd.Series, low_prices: pd.Series, close_prices: pd.Series, period: int = 14) -> pd.Series:
    """
    Calculate Average True Range (ATR).
    TR = max[(High - Low), abs(High - Previous_Close), abs(Low - Previous_Close)]
    ATR = Wilder's smoothing of TR

    Args:
        high_prices (pd.Series): Series of high prices.
        low_prices (pd.Series): Series of low prices.
        close_prices (pd.Series): Series of closing prices.
        period (int): The period for ATR calculation. Default is 14.

    Returns:
        pd.Series: Series containing the ATR values.
    """
    if not all(isinstance(s, pd.Series) for s in [high_prices, low_prices, close_prices]):
        raise TypeError("high_prices, low_prices, and close_prices must be pandas Series.")
    if not (len(high_prices) == len(low_prices) == len(close_prices)):
        raise ValueError("All price series must have the same length.")
    if not isinstance(period, int) or period <= 0:
        raise ValueError("period must be a positive integer.")
    if len(close_prices) < period: # Not enough data for a meaningful ATR
        return pd.Series(index=close_prices.index, dtype=float, name=f'ATR_{period}')

    prev_close = close_prices.shift(1)

    # Calculate the three components of True Range
    tr1 = high_prices - low_prices
    tr2 = (high_prices - prev_close).abs()
    tr3 = (low_prices - prev_close).abs()

    # DataFrame to find the max per row, skipping rows with NaNs in price data.
    tr_df = pd.concat([tr1, tr2, tr3], axis=1)
    true_range = tr_df.max(axis=1, skipna=False) # skipna=False ensures that if any price is NaN, TR is NaN

    # Calculate ATR using Wilder's smoothing (EMA with alpha = 1/period)
    # adjust=False is crucial for matching the traditional recursive formula.
    atr = true_range.ewm(alpha=1/period, adjust=False, min_periods=period).mean()

    atr.name = f'ATR_{period}'
    return atr

def average_true_range_optimized(high_prices: pd.Series, low_prices: pd.Series, close_prices: pd.Series, period: int = 14) -> pd.Series:
    """
    Optimized version of Average True Range (ATR) calculation using NumPy operations.

    Performance improvements:
    - Direct NumPy array operations
    - Vectorized True Range calculation
    - Optimized memory usage

    Args:
        high_prices (pd.Series): Series of high prices.
        low_prices (pd.Series): Series of low prices.
        close_prices (pd.Series): Series of closing prices.
        period (int): The period for ATR calculation. Default is 14.

    Returns:
        pd.Series: Series containing the ATR values.
    """
    if not all(isinstance(s, pd.Series) for s in [high_prices, low_prices, close_prices]):
        raise TypeError("high_prices, low_prices, and close_prices must be pandas Series.")
    if not (len(high_prices) == len(low_prices) == len(close_prices)):
        raise ValueError("All price series must have the same length.")
    if not isinstance(period, int) or period <= 0:
        raise ValueError("period must be a positive integer.")
    if len(close_prices) < period:
        return pd.Series(index=close_prices.index, dtype=float, name=f'ATR_{period}')

    # Convert to numpy arrays for faster computation
    high_np = high_prices.values
    low_np = low_prices.values
    close_np = close_prices.values

    # Calculate True Range using vectorized operations
    n = len(close_np)
    true_range = np.zeros(n)

    # First TR is just high - low
    true_range[0] = high_np[0] - low_np[0]

    # Vectorized calculation for the rest
    if n > 1:
        tr1 = high_np[1:] - low_np[1:]
        tr2 = np.abs(high_np[1:] - close_np[:-1])
        tr3 = np.abs(low_np[1:] - close_np[:-1])

        true_range[1:] = np.maximum(tr1, np.maximum(tr2, tr3))

    # Calculate ATR using Wilder's smoothing
    atr_values = np.full(n, np.nan)
    alpha = 1.0 / period

    # Initialize first ATR value as simple average of first 'period' TR values
    if n >= period:
        atr_values[period - 1] = np.mean(true_range[:period])

        # Apply Wilder's smoothing for subsequent values
        for i in range(period, n):
            atr_values[i] = alpha * true_range[i] + (1 - alpha) * atr_values[i - 1]

    atr = pd.Series(atr_values, index=close_prices.index, name=f'ATR_{period}')
    return atr

def moving_average_convergence_divergence(
    close_prices: pd.Series, 
    fast_period: int = 12, 
    slow_period: int = 26, 
    signal_period: int = 9
) -> tuple[pd.Series, pd.Series, pd.Series]:
    """
    Calculate Moving Average Convergence Divergence (MACD).
    MACD Line = EMA(close, fast_period) - EMA(close, slow_period)
    Signal Line = EMA(MACD Line, signal_period)
    MACD Histogram = MACD Line - Signal Line

    Args:
        close_prices (pd.Series): Series of closing prices.
        fast_period (int): The period for the fast EMA. Default is 12.
        slow_period (int): The period for the slow EMA. Default is 26.
        signal_period (int): The period for the signal line EMA. Default is 9.

    Returns:
        tuple[pd.Series, pd.Series, pd.Series]: A tuple containing the MACD Line, Signal Line, and MACD Histogram.
    """
    if not isinstance(close_prices, pd.Series):
        raise TypeError("close_prices must be a pandas Series.")
    if not all(isinstance(p, int) and p > 0 for p in [fast_period, slow_period, signal_period]):
        raise ValueError("All period arguments must be positive integers.")
    if fast_period >= slow_period:
        raise ValueError("fast_period must be less than slow_period.")
    if len(close_prices) < slow_period:
        # Not enough data for calculation, return empty series with correct names
        name_macd = f'MACD_{fast_period}_{slow_period}'
        name_signal = f'MACD_signal_{signal_period}'
        name_hist = f'MACD_hist_{fast_period}_{slow_period}_{signal_period}'
        empty_series = pd.Series(index=close_prices.index, dtype=float)
        return empty_series.rename(name_macd), empty_series.rename(name_signal), empty_series.rename(name_hist)

    # Calculate Fast and Slow EMAs using standard formula
    ema_fast = close_prices.ewm(span=fast_period, adjust=False, min_periods=fast_period).mean()
    ema_slow = close_prices.ewm(span=slow_period, adjust=False, min_periods=slow_period).mean()

    # Calculate MACD Line
    macd_line = ema_fast - ema_slow
    macd_line.name = f'MACD_{fast_period}_{slow_period}'

    # Calculate Signal Line
    signal_line = macd_line.ewm(span=signal_period, adjust=False, min_periods=signal_period).mean()
    signal_line.name = f'MACD_signal_{signal_period}'

    # Calculate MACD Histogram
    macd_histogram = macd_line - signal_line
    macd_histogram.name = f'MACD_hist_{fast_period}_{slow_period}_{signal_period}'
    
    return macd_line, signal_line, macd_histogram

def stochastic_oscillator(
    high_prices: pd.Series, 
    low_prices: pd.Series, 
    close_prices: pd.Series, 
    k_period: int = 14, 
    d_period: int = 3
) -> tuple[pd.Series, pd.Series]:
    """
    Calculate the Stochastic Oscillator (%K and %D).
    %K = 100 * (Current Close - Lowest Low) / (Highest High - Lowest Low)
    %D = d_period SMA of %K

    Args:
        high_prices (pd.Series): Series of high prices.
        low_prices (pd.Series): Series of low prices.
        close_prices (pd.Series): Series of closing prices.
        k_period (int): The lookback period for %K calculation. Default is 14.
        d_period (int): The smoothing period for %D calculation. Default is 3.

    Returns:
        tuple[pd.Series, pd.Series]: A tuple containing the %K and %D lines.
    """
    if not all(isinstance(s, pd.Series) for s in [high_prices, low_prices, close_prices]):
        raise TypeError("high_prices, low_prices, and close_prices must be pandas Series.")
    if not (len(high_prices) == len(low_prices) == len(close_prices)):
        raise ValueError("All price series must have the same length.")
    if not all(isinstance(p, int) and p > 0 for p in [k_period, d_period]):
        raise ValueError("All period arguments must be positive integers.")
    if len(close_prices) < k_period:
        name_k = f'STOCHk_{k_period}'
        name_d = f'STOCHd_{d_period}'
        empty_series = pd.Series(index=close_prices.index, dtype=float)
        return empty_series.rename(name_k), empty_series.rename(name_d)

    # Calculate Highest High and Lowest Low over the k_period
    highest_high = high_prices.rolling(window=k_period).max()
    lowest_low = low_prices.rolling(window=k_period).min()

    # Calculate %K
    numerator = close_prices - lowest_low
    denominator = highest_high - lowest_low
    
    # Handle division by zero: if denominator is 0, the price range was zero.
    # We set %K to 100, assuming price is at its high for the period.
    percent_k_values = np.where(denominator > 0, 100 * numerator / denominator, 100.0)
    percent_k = pd.Series(percent_k_values, index=close_prices.index)
    
    # Calculate %D as the SMA of %K
    percent_d = percent_k.rolling(window=d_period).mean()

    percent_k.name = f'STOCHk_{k_period}'
    percent_d.name = f'STOCHd_{d_period}'

    return percent_k, percent_d

# Example Usage (optional, for testing)
if __name__ == "__main__":
    # Create sample data
    data = {
        'Open': [100, 101, 102, 100, 103, 105, 106, 108, 110, 109, 107, 108, 110, 112, 115, 113, 112, 110, 111, 114],
        'High': [102, 103, 104, 103, 106, 107, 108, 110, 112, 111, 109, 110, 112, 114, 117, 115, 114, 112, 113, 116],
        'Low':  [99, 100, 101, 99, 101, 104, 105, 107, 108, 108, 106, 107, 109, 111, 112, 112, 110, 109, 110, 112],
        'Close':[101, 102, 103, 101, 105, 106, 107, 109, 111, 110, 108, 109, 111, 113, 116, 114, 111, 110, 112, 115],
        'Volume': [1000, 1100, 1200, 1300, 1000, 1500, 1600, 1700, 1800, 1900, 1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900]
    }
    df = pd.DataFrame(data)
    df.index = pd.to_datetime([f'2023-01-{i:02d}' for i in range(1, len(df) + 1)])

    print("Sample DataFrame:")
    print(df.head())
    print("\nCalculating indicators with period = 14 (default where applicable)...")
    
    # Calculate ROC (default period 14)
    df['ROC_14'] = rate_of_change(df['Close']) 
    print("\nRate of Change (14):")
    print(df[['Close', 'ROC_14']].tail())

    # Calculate RSI (default period 14)
    df['RSI_14'] = relative_strength_index(df['Close'])
    print("\nRelative Strength Index (14):")
    print(df[['Close', 'RSI_14']].tail())

    # Calculate ATR (default period 14)
    df['ATR_14'] = average_true_range(df['High'], df['Low'], df['Close'])
    print("\nAverage True Range (14):")
    print(df[['High', 'Low', 'Close', 'ATR_14']].tail())

    # Calculate MACD (default periods 12, 26, 9)
    df['MACD'], df['MACD_Signal'], df['MACD_Hist'] = moving_average_convergence_divergence(df['Close'])
    print("\nMoving Average Convergence Divergence (12, 26, 9):")
    print(df[['Close', 'MACD', 'MACD_Signal', 'MACD_Hist']].tail())

    # Calculate Stochastic Oscillator (default periods 14, 3)
    df['STOCHk_14'], df['STOCHd_3'] = stochastic_oscillator(df['High'], df['Low'], df['Close'])
    print("\nStochastic Oscillator (14, 3):")
    print(df[['Close', 'STOCHk_14', 'STOCHd_3']].tail())

    print("\n--- Full DataFrame with Indicators ---")
    print(df.tail())

    print("\n--- Test with shorter period (e.g., 5) ---")
    df['ROC_5'] = rate_of_change(df['Close'], period=5)
    df['RSI_5'] = relative_strength_index(df['Close'], period=5)
    df['ATR_5'] = average_true_range(df['High'], df['Low'], df['Close'], period=5)
    df['MACD_5_10_4'], df['MACD_Signal_5_10_4'], _ = moving_average_convergence_divergence(df['Close'], fast_period=5, slow_period=10, signal_period=4)
    df['STOCHk_5'], df['STOCHd_3_s'] = stochastic_oscillator(df['High'], df['Low'], df['Close'], k_period=5, d_period=3)
    print(df[['Close', 'ROC_5', 'RSI_5', 'ATR_5', 'MACD_5_10_4', 'STOCHk_5', 'STOCHd_3_s']].tail())

    print("\n--- Test with insufficient data for period=14 ---")
    short_close = df['Close'].head(10)
    print(f"Length of short_close: {len(short_close)}")
    roc_short = rate_of_change(short_close, period=14)
    rsi_short = relative_strength_index(short_close, period=14)
    atr_short = average_true_range(df['High'].head(10), df['Low'].head(10), short_close, period=14)
    macd_line_short, _, _ = moving_average_convergence_divergence(short_close) # Uses defaults 12, 26
    stoch_k_short, stoch_d_short = stochastic_oscillator(df['High'].head(10), df['Low'].head(10), short_close, k_period=14)
    print(f"ROC_14 on short data (all NaN expected for period=14):\n{roc_short}")
    print(f"RSI_14 on short data (all NaN expected for period=14):\n{rsi_short}")
    print(f"ATR_14 on short data (all NaN expected for period=14):\n{atr_short}")
    print(f"MACD on short data (all NaN expected as len < 26):\n{macd_line_short}")
    print(f"Stochastic on short data (all NaN expected as len < 14):\n{stoch_k_short}")

    print("\n--- Test with insufficient data for period=5 ---")
    roc_short_5 = rate_of_change(short_close, period=5)
    rsi_short_5 = relative_strength_index(short_close, period=5)
    atr_short_5 = average_true_range(df['High'].head(10), df['Low'].head(10), short_close, period=5)
    macd_s_5, _, _ = moving_average_convergence_divergence(short_close, fast_period=5, slow_period=8, signal_period=3)
    stoch_k_s_5, stoch_d_s_5 = stochastic_oscillator(df['High'].head(10), df['Low'].head(10), short_close, k_period=5, d_period=3)
    print(f"ROC_5 on short data (first 5 NaN):\n{roc_short_5}") # first 'period' values will be NaN
    print(f"RSI_5 on short data (first 'period' values for avg, so up to period+1 for first RSI):\n{rsi_short_5}")
    print(f"ATR_5 on short data (first 'period' values for avg):\n{atr_short_5}")
    print(f"MACD(5,8,3) on short data:\n{macd_s_5}")
    print(f"Stochastic(5,3) on short data:\n{stoch_k_s_5}")