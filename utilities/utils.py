import os
import json
import pickle
import logging
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import glob
import warnings # For library-friendly warnings

#####################################################
# Path and File System Utilities
#####################################################

def get_project_root():
    """
    Dynamically finds the project root directory.
    Assumes this script is in a subdirectory of the project root (e.g., 'utilities').
    """
    # This file is in /<project_root>/utilities/
    # os.path.abspath(__file__) -> /<project_root>/utilities/utils.py
    # os.path.dirname(...) -> /<project_root>/utilities
    # os.path.dirname(...) -> /<project_root>
    return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

#####################################################
# 日期和时间处理工具
#####################################################

def str_to_datetime(date_str, fmt="%Y-%m-%d"):
    """字符串转datetime对象"""
    return datetime.strptime(date_str, fmt)

def datetime_to_str(dt, fmt="%Y-%m-%d"):
    """datetime对象转字符串"""
    return dt.strftime(fmt)

#####################################################
# 交易日历工具
#####################################################

_trade_dates_list = None
_trade_dates_set = None

def get_futu_trading_calendar(quote_ctx, market='HK', days_back=30):
    """
    使用富途API获取交易日历

    Args:
        quote_ctx: 富途API连接对象
        market: 市场类型，默认'HK'
        days_back: 往前查询的天数，默认30天

    Returns:
        list: 交易日期字符串列表，格式为'YYYY-MM-DD'
    """
    try:
        from futu import TradeDateMarket, RET_OK
        import datetime

        end_date = datetime.datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.datetime.now() - datetime.timedelta(days=days_back)).strftime('%Y-%m-%d')

        # 根据市场类型设置参数
        if market.upper() == 'HK':
            market_param = TradeDateMarket.HK
        elif market.upper() == 'US':
            market_param = TradeDateMarket.US
        elif market.upper() == 'CN':
            market_param = TradeDateMarket.CN
        else:
            market_param = TradeDateMarket.HK  # 默认港股

        ret, data = quote_ctx.request_trading_days(market=market_param, start=start_date, end=end_date)

        if ret == RET_OK and data:
            # 提取交易日期
            trading_days = [item['time'] for item in data if item.get('trade_date_type') == 'WHOLE']
            trading_days.sort()
            return trading_days
        else:
            print(f"Warning: Could not get trading calendar: {data}")
            return None

    except Exception as e:
        print(f"Error getting Futu trading calendar: {e}")
        return None

def get_last_futu_trading_day(quote_ctx, market='HK', days_back=30):
    """
    获取最近的交易日

    Args:
        quote_ctx: 富途API连接对象
        market: 市场类型，默认'HK'
        days_back: 往前查询的天数，默认30天

    Returns:
        str: 最近交易日，格式为'YYYY-MM-DD'，如果获取失败则返回None
    """
    trading_days = get_futu_trading_calendar(quote_ctx, market, days_back)
    if trading_days:
        import datetime
        today = datetime.datetime.now().strftime('%Y-%m-%d')

        # 找到今天或之前最近的交易日
        last_trading_day = None
        for day in reversed(trading_days):
            if day <= today:
                last_trading_day = day
                break

        return last_trading_day

    # 如果无法获取交易日历，使用简单的工作日逻辑作为备选
    import datetime
    today = datetime.datetime.now()
    while today.weekday() >= 5:  # 周六=5, 周日=6
        today -= datetime.timedelta(days=1)
    return today.strftime('%Y-%m-%d')

def is_futu_trading_day(quote_ctx, date_str, market='HK', days_back=30):
    """
    判断指定日期是否为交易日

    Args:
        quote_ctx: 富途API连接对象
        date_str: 日期字符串，格式为'YYYY-MM-DD'
        market: 市场类型，默认'HK'
        days_back: 往前查询的天数，默认30天

    Returns:
        bool: 是否为交易日
    """
    trading_days = get_futu_trading_calendar(quote_ctx, market, days_back)
    if trading_days:
        return date_str in trading_days

    # 备选方案：简单的工作日判断
    import datetime
    date_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d')
    return date_obj.weekday() < 5

def load_trade_calendar(calendar_path=None, force_reload=False):
    """
    加载交易日历数据.
    返回: 包含交易日期的排序列表.
    """
    global _trade_dates_list, _trade_dates_set
    
    if _trade_dates_list is not None and not force_reload:
        return _trade_dates_list

    if calendar_path is None:
        try:
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            calendar_path = os.path.join(base_dir, 'ashare', 'trade_date_history.csv')
        except NameError: 
            raise ValueError("Cannot determine default calendar path. Please provide calendar_path.")

    if not os.path.exists(calendar_path):
        raise FileNotFoundError(f"交易日历文件未找到: {calendar_path}")
    
    try:
        df = pd.read_csv(calendar_path)
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        _trade_dates_list = sorted(list(set(df['trade_date'].tolist())))
        _trade_dates_set = set(_trade_dates_list)
        return _trade_dates_list
    except Exception as e:
        _trade_dates_list = None
        _trade_dates_set = None
        raise RuntimeError(f"加载交易日历失败: {e}")


def _ensure_calendar_loaded():
    if _trade_dates_list is None:
        load_trade_calendar()
    if _trade_dates_list is None: 
        raise RuntimeError("交易日历未加载或加载失败。")
    return _trade_dates_list, _trade_dates_set

def get_next_trading_day(date, trading_days=None):
    """获取下一个交易日"""
    tdays, _ = _ensure_calendar_loaded()
    current_days = trading_days if trading_days is not None else tdays
    
    if isinstance(date, str):
        date = str_to_datetime(date) 
    if not isinstance(date, datetime): 
        date = pd.to_datetime(date).to_pydatetime()

    try:
        idx = current_days.index(date)
        return current_days[idx + 1] if idx + 1 < len(current_days) else None
    except ValueError: 
        for td in current_days:
            if td > date:
                return td
        return None


def get_prev_trading_day(date, trading_days=None):
    """获取上一个交易日"""
    tdays, _ = _ensure_calendar_loaded()
    current_days = trading_days if trading_days is not None else tdays

    if isinstance(date, str):
        date = str_to_datetime(date)
    if not isinstance(date, datetime):
        date = pd.to_datetime(date).to_pydatetime()
        
    try:
        idx = current_days.index(date)
        return current_days[idx - 1] if idx > 0 else None
    except ValueError: 
        prev_td = None
        for td in current_days:
            if td < date:
                prev_td = td
            else: 
                break
        return prev_td


def is_trade_date(date):
    """判断给定日期是否为交易日"""
    _, tdays_set = _ensure_calendar_loaded()
    if isinstance(date, str):
        date = pd.to_datetime(date) 
    elif isinstance(date, datetime): 
        date = pd.Timestamp(date)
    return date in tdays_set

def get_trade_dates(start_date=None, end_date=None):
    """获取指定日期范围内的所有交易日"""
    tdays, _ = _ensure_calendar_loaded()
    
    if start_date is None and end_date is None:
        return list(tdays) 
    
    s_date = pd.to_datetime(start_date) if start_date else None
    e_date = pd.to_datetime(end_date) if end_date else None
    
    filtered_dates = tdays
    if s_date:
        filtered_dates = [d for d in filtered_dates if d >= s_date]
    if e_date:
        filtered_dates = [d for d in filtered_dates if d <= e_date]
    return filtered_dates

def align_to_trade_dates(df, date_column='日期'):
    """将数据框按日期列对齐到交易日历 (移除包含非交易日的行)"""
    _ensure_calendar_loaded()
    df[date_column] = pd.to_datetime(df[date_column])
    original_len = len(df)
    df_aligned = df[df[date_column].apply(is_trade_date)].copy()
    if len(df_aligned) < original_len:
        warnings.warn(f"数据对齐交易日历: {original_len - len(df_aligned)}行非交易日数据已移除。")
    return df_aligned

def fill_missing_trade_dates(df, date_column='日期', fill_method='ffill', 
                             price_cols=None, volume_cols=None, other_cols_fill_method='ffill'):
    """
    基于交易日历填充DataFrame中的缺失交易日。
    - price_cols: 价格相关列 (如 开高低收), 使用fill_method填充.
    - volume_cols: 成交量/额相关列, 用0填充.
    - other_cols: 其他列, 使用other_cols_fill_method填充.
    """
    _ensure_calendar_loaded()
    df[date_column] = pd.to_datetime(df[date_column])
    
    if df.empty:
        warnings.warn("输入 DataFrame 为空，无法填充缺失交易日。")
        return df

    min_date, max_date = df[date_column].min(), df[date_column].max()
    all_tdates_in_range = get_trade_dates(min_date, max_date)
    
    complete_df = pd.DataFrame({date_column: all_tdates_in_range})
    merged_df = pd.merge(complete_df, df, on=date_column, how='left').sort_values(date_column)
    
    cols_to_fill = merged_df.columns.drop(date_column)
    
    if merged_df[cols_to_fill].isnull().values.any(): 
        if price_cols:
            valid_price_cols = [col for col in price_cols if col in merged_df.columns]
            if valid_price_cols and fill_method:
                if fill_method == 'ffill':
                    merged_df[valid_price_cols] = merged_df[valid_price_cols].ffill()
                elif fill_method == 'bfill':
                    merged_df[valid_price_cols] = merged_df[valid_price_cols].bfill()
                else:
                    merged_df[valid_price_cols] = merged_df[valid_price_cols].fillna(method=fill_method)
        
        if volume_cols:
            valid_volume_cols = [col for col in volume_cols if col in merged_df.columns]
            if valid_volume_cols: 
                merged_df[valid_volume_cols] = merged_df[valid_volume_cols].fillna(0)

        filled_cols = (price_cols or []) + (volume_cols or [])
        other_cols = [col for col in cols_to_fill if col not in filled_cols]
        if other_cols and other_cols_fill_method:
            if other_cols_fill_method == 'ffill':
                merged_df[other_cols] = merged_df[other_cols].ffill()
            elif other_cols_fill_method == 'bfill':
                merged_df[other_cols] = merged_df[other_cols].bfill()
            else:
                merged_df[other_cols] = merged_df[other_cols].fillna(method=other_cols_fill_method)
        
        num_filled = len(all_tdates_in_range) - len(df)
        if num_filled > 0:
            warnings.warn(f"已基于交易日历填充 {num_filled} 个缺失交易日的数据。")
            
    return merged_df

def get_trade_date_distance(start_date, end_date):
    """计算两个日期之间的交易日天数 (不含起始日期)"""
    tdays, _ = _ensure_calendar_loaded()
    s_date = pd.to_datetime(start_date)
    e_date = pd.to_datetime(end_date)
    return len([d for d in tdays if s_date < d <= e_date])

def get_latest_trade_date():
    """获取最新的交易日期"""
    tdays, _ = _ensure_calendar_loaded()
    return tdays[-1] if tdays else None

def get_latest_past_trade_date(current_date=None):
    """获取指定日期（默认为当前日期）或之前的最近交易日"""
    tdays, _ = _ensure_calendar_loaded()
    if not tdays: return None
    
    target_date = pd.Timestamp.now().normalize() if current_date is None else pd.to_datetime(current_date)
    
    for i in range(len(tdays) - 1, -1, -1):
        if tdays[i] <= target_date:
            return tdays[i]
    return None 

#In your utils.py, ADD these functions or decide if current d

def is_last_trade_day_of_period(date_to_check: pd.Timestamp, period_type: str) -> bool:
    """
    Checks if date_to_check is the last trading day of its week or month.
    Relies on _trade_dates_list being loaded.
    """
    _ensure_calendar_loaded() # Make sure _trade_dates_list and _trade_dates_set are populated
    
    date_to_check = pd.to_datetime(date_to_check).normalize() # Ensure it's a Timestamp at midnight

    # 1. Check if date_to_check is a trading day
    if not is_trade_date(date_to_check): # Uses your existing is_trade_date
        # print(f"Debug: {date_to_check.strftime('%Y-%m-%d')} is not a trading day.")
        return False

    # 2. Find the next trading day *after* date_to_check
    next_td = get_next_trading_day(date_to_check) # Uses your existing get_next_trading_day

    if next_td is None:
        # print(f"Debug: {date_to_check.strftime('%Y-%m-%d')} is the last known trading day in calendar.")
        return True # If there's no next trading day, it's the last of any period

    # Convert to pd.Timestamp if they are datetime.datetime for consistent methods
    current_trade_day_ts = pd.Timestamp(date_to_check)
    next_trade_day_ts = pd.Timestamp(next_td)
    
    # print(f"Debug: Current TD: {current_trade_day_ts}, Next TD: {next_trade_day_ts}")

    if period_type == 'week':
        # isocalendar returns (ISO year, ISO week number, ISO weekday)
        return current_trade_day_ts.isocalendar()[1] != next_trade_day_ts.isocalendar()[1]
    elif period_type == 'month':
        return current_trade_day_ts.month != next_trade_day_ts.month
    else:
        raise ValueError(f"Unsupported period_type: {period_type}")

def is_last_trade_day_of_week(date_to_check: pd.Timestamp) -> bool:
    """Checks if date_to_check is the last trading day of its week."""
    return is_last_trade_day_of_period(date_to_check, 'week')

def is_last_trade_day_of_month(date_to_check: pd.Timestamp) -> bool:
    """Checks if date_to_check is the last trading day of its month."""
    return is_last_trade_day_of_period(date_to_check, 'month')

#####################################################
# 数据清洗和预处理工具 (通用)
#####################################################

def fillna_value(df_or_series, value=0):
    """统一缺失值填充"""
    return df_or_series.fillna(value)

def standardize_series(series):
    """数据标准化（均值为0，方差为1）"""
    return (series - series.mean()) / series.std()

def remove_outliers_std(series, n_std=3):
    """去除n倍标准差以外的异常值 (返回Series)"""
    mean, std = series.mean(), series.std()
    return series[np.abs(series - mean) <= (n_std * std)]

def smooth_series_rolling_mean(series, window=5):
    """简单滑动平均平滑"""
    return series.rolling(window=window, min_periods=1).mean() 

#####################################################
# 股票数据处理工具
#####################################################

def get_stock_name_from_csv(stock_code, lookup_dir):
    """
    从指定目录的 *_stock_list.csv 文件中获取股票名称。
    """
    stock_list_files = glob.glob(os.path.join(lookup_dir, '*_stock_list.csv'))
    if not stock_list_files:
        stock_list_files = glob.glob(os.path.join(lookup_dir, '..', '*_stock_list.csv')) 

    # 清理股票代码，去除前缀和填充0
    stock_code_clean = str(stock_code).zfill(6).replace('SH', '').replace('SZ', '').replace('sh', '').replace('sz', '')
    stock_code_clean = stock_code_clean[-6:] 

    code_keywords = ['股票代码', 'stock_code', 'code', '代码']
    name_keywords = ['股票简称', 'stock_name', 'name', '简称']

    # 1. 首先尝试从列表文件中获取名称
    for fpath in stock_list_files:
        try:
            df_list = pd.read_csv(fpath, encoding='utf-8', dtype=str)
        except:
            try:
                df_list = pd.read_csv(fpath, encoding='gbk', dtype=str)
            except:
                continue
        
        code_col, name_col = None, None
        for col in df_list.columns:
            col_lower = col.lower().replace(' ', '')
            if not code_col and any(kw.lower() in col_lower for kw in code_keywords): code_col = col
            if not name_col and any(kw.lower() in col_lower for kw in name_keywords): name_col = col
        
        if code_col and name_col:
            df_list[code_col] = df_list[code_col].str.replace(r'\D', '', regex=True).str.zfill(6).str[-6:]
            match = df_list[df_list[code_col] == stock_code_clean]
            if not match.empty:
                return match.iloc[0][name_col]
    
    # 2. 如果没找到，尝试从CSV数据文件中获取股票名称
    # 新的文件命名格式
    csv_path = os.path.join(lookup_dir, f'{stock_code}_daily_hfq.csv')
    if not os.path.exists(csv_path):
        # 尝试旧格式
        csv_path = os.path.join(lookup_dir, f'{stock_code}.csv')
        if not os.path.exists(csv_path):
            return None
    
    try:
        df = pd.read_csv(csv_path)
        if 'stock_name' in df.columns and not df['stock_name'].empty:
            return df['stock_name'].iloc[0]
        elif 'name' in df.columns and not df['name'].empty:
            return df['name'].iloc[0]
    except:
        pass
    
    return None


def load_stock_names(project_root=None):
    """
    从上海和深圳股票列表中批量加载所有股票名称。
    
    Args:
        project_root: 项目根目录路径，如果为None则自动推断
        
    Returns:
        dict: 股票代码到股票名称的映射字典
    """
    if project_root is None:
        # 自动推断项目根目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
    
    stock_names = {}
    
    # 加载上海股票列表
    sh_stock_path = os.path.join(project_root, 'ashare', 'sh_stock_list.csv')
    if os.path.exists(sh_stock_path):
        try:
            sh_df = pd.read_csv(sh_stock_path)
            for _, row in sh_df.iterrows():
                stock_code = str(row['证券代码']).zfill(6)
                stock_name = row['证券简称']
                stock_names[stock_code] = stock_name
        except Exception as e:
            warnings.warn(f"加载上海股票列表失败: {e}")
    
    # 加载深圳股票列表
    sz_stock_path = os.path.join(project_root, 'ashare', 'sz_stock_list.csv')
    if os.path.exists(sz_stock_path):
        try:
            sz_df = pd.read_csv(sz_stock_path)
            for _, row in sz_df.iterrows():
                stock_code = str(row['证券代码']).zfill(6)
                stock_name = row['证券简称']
                stock_names[stock_code] = stock_name
        except Exception as e:
            warnings.warn(f"加载深圳股票列表失败: {e}")
    
    if stock_names:
        print(f"成功加载 {len(stock_names)} 个股票名称")
    else:
        warnings.warn("未找到任何股票名称数据")
    
    return stock_names

def load_stock_data(stock_code, data_dir, start_date=None, end_date=None, 
                    apply_trade_calendar_cleaning=True, extra_days_for_calc=250):
    """
    加载股票CSV数据, 可选日期过滤和交易日历清洗。
    返回: (df_display, df_for_calc, title_prefix)
        df_display: 筛选和清洗后用于显示/分析的数据。
        df_for_calc: 包含额外历史数据用于指标计算的数据。
        title_prefix: "代码 名称" 或 "代码"。
    """
    # 新的文件命名格式：{stock_code}_daily_hfq.csv
    file_path = os.path.join(data_dir, f'{stock_code}_daily_hfq.csv')
    if not os.path.exists(file_path):
        # 尝试旧格式
        old_file_path = os.path.join(data_dir, f'{stock_code}.csv')
        if os.path.exists(old_file_path):
            warnings.warn(f"使用旧格式文件: {old_file_path}")
            file_path = old_file_path
        else:
            warnings.warn(f"未找到股票 {stock_code} 的数据文件: {file_path}")
            return None, None, stock_code
        
    try:
        df = pd.read_csv(file_path, parse_dates=['日期'])
        df['日期'] = pd.to_datetime(df['日期'])
        df = df.sort_values('日期').reset_index(drop=True)
    except Exception as e:
        warnings.warn(f"读取或处理文件 {file_path} 时出错: {e}")
        return None, None, stock_code

    stock_name = df.get('stock_name', pd.Series(dtype=str)).first_valid_index()
    stock_name = df['stock_name'][stock_name] if stock_name is not None else None
    if not stock_name:
        stock_name = get_stock_name_from_csv(stock_code, data_dir)
    
    title_prefix = f"{stock_code} {stock_name}" if stock_name else stock_code

    if apply_trade_calendar_cleaning:
        _ensure_calendar_loaded() 
        df = align_to_trade_dates(df, date_column='日期') 
        price_cols = ['开盘', '收盘', '最高', '最低', '前收盘']
        volume_cols = ['成交量', '成交额']
        df = fill_missing_trade_dates(df, date_column='日期', fill_method='ffill',
                                      price_cols=price_cols, volume_cols=volume_cols)
        if '成交量' in df.columns:
            df = df[df['成交量'] > 0] 

    if df.empty:
        warnings.warn(f"清洗后股票 {stock_code} 数据为空。")
        return None, None, title_prefix
    
    s_date_dt = pd.to_datetime(start_date) if start_date else None
    e_date_dt = pd.to_datetime(end_date) if end_date else None

    df_for_calc = df.copy()
    if s_date_dt:
        _ensure_calendar_loaded()
        temp_s_date_for_calc = s_date_dt
        for _ in range(extra_days_for_calc):
            prev_day = get_prev_trading_day(temp_s_date_for_calc)
            if prev_day is None: break 
            temp_s_date_for_calc = prev_day
        
        df_for_calc = df_for_calc[df_for_calc['日期'] >= temp_s_date_for_calc]

    if e_date_dt:
        df_for_calc = df_for_calc[df_for_calc['日期'] <= e_date_dt]

    df_display = df_for_calc.copy()
    if s_date_dt:
        df_display = df_display[df_display['日期'] >= s_date_dt]
    
    if df_display.empty:
        warnings.warn(f"在指定日期范围内 ({start_date} - {end_date}) 未找到股票 {stock_code} 的数据。")
        return None, df_for_calc if not df_for_calc.empty else None, title_prefix
    
    return df_display, df_for_calc, title_prefix


#####################################################
# 文件操作工具
#####################################################

def read_json_file(path):
    with open(path, 'r', encoding='utf-8') as f: return json.load(f)

def save_json_file(obj, path):
    with open(path, 'w', encoding='utf-8') as f: json.dump(obj, f, ensure_ascii=False, indent=4)

def save_pickle_file(obj, path):
    with open(path, 'wb') as f: pickle.dump(obj, f)

def load_pickle_file(path):
    with open(path, 'rb') as f: return pickle.load(f)

#####################################################
# 日志配置工具
#####################################################

def setup_basic_logger(name="app_logger", log_file="app.log", level=logging.INFO,
                       fmt='%(asctime)s %(levelname)s %(message)s'):
    logger = logging.getLogger(name)
    if not logger.handlers: 
        logger.setLevel(level)
        formatter = logging.Formatter(fmt)
        
        fh = logging.FileHandler(log_file, encoding='utf-8')
        fh.setFormatter(formatter)
        logger.addHandler(fh)
        
    return logger

#####################################################
# 统计计算工具
#####################################################

def safe_division(numerator, denominator, default_val=0.0):
    """安全除法，避免除零错误。"""
    if denominator == 0 or np.isnan(denominator) or np.isinf(denominator):
        return default_val
    try:
        result = numerator / denominator
        return default_val if np.isnan(result) or np.isinf(result) else result
    except ZeroDivisionError:
        return default_val

def series_to_percent_change(series, fill_na=0.0):
    """计算Series的百分比变化，可指定NaN填充值。"""
    return series.pct_change().fillna(fill_na)

def calculate_sharpe_ratio(returns_series, risk_free_rate=0.0, periods_per_year=252):
    """
    计算夏普比率。
    returns_series: 收益率 Pandas Series (日收益率等).
    risk_free_rate: 无风险利率 (年化, e.g., 0.02 for 2%).
    periods_per_year: 一年中的期数 (日收益率为252, 月收益率为12).
    """
    if not isinstance(returns_series, pd.Series) or returns_series.empty:
        return 0.0
    
    excess_returns = returns_series - (risk_free_rate / periods_per_year)
    mean_excess_return = excess_returns.mean()
    std_dev_excess_return = excess_returns.std()
    
    if std_dev_excess_return == 0 or np.isnan(std_dev_excess_return):
        return 0.0
        
    sharpe = mean_excess_return / std_dev_excess_return
    return sharpe * np.sqrt(periods_per_year)


def get_series_statistics(series):
    """计算数据序列的各种统计指标"""
    if not isinstance(series, pd.Series) or series.empty:
        return {}
    
    stats = {
        'count': series.count(),
        'mean': series.mean(),
        'median': series.median(),
        'std': series.std(),
        'min': series.min(),
        'max': series.max(),
        'skew': series.skew(),
        'kurt': series.kurtosis(),
        'q01': series.quantile(0.01), 
        'q05': series.quantile(0.05), 
        'q25': series.quantile(0.25),
        'q75': series.quantile(0.75),
        'q95': series.quantile(0.95),
        'q99': series.quantile(0.99), 
    }
    if stats['count'] > 0 :
        stats['positive_ratio'] = (series > 0).sum() / stats['count'] * 100
        stats['negative_ratio'] = (series < 0).sum() / stats['count'] * 100
        stats['zero_ratio'] = (series == 0).sum() / stats['count'] * 100
        
        positive_values = series[series > 0]
        negative_values = series[series < 0]
        stats['positive_mean'] = positive_values.mean() if not positive_values.empty else 0
        stats['negative_mean'] = negative_values.mean() if not negative_values.empty else 0
    else:
        stats.update({'positive_ratio':0, 'negative_ratio':0, 'zero_ratio':0, 'positive_mean':0, 'negative_mean':0})

    return {k: (round(v, 4) if isinstance(v, (float, np.floating)) else v) for k,v in stats.items()}

#####################################################
# DataFrame验证工具
#####################################################

def validate_dataframe_basic(df, required_columns=None, date_column='日期', check_na=True):
    """
    验证DataFrame是否满足基本要求。
    返回: (is_valid, message_list)
    """
    messages = []
    if df is None or df.empty:
        messages.append("数据框为空。")
        return False, messages
    
    if required_columns:
        missing_cols = [col for col in required_columns if col not in df.columns]
        if missing_cols:
            messages.append(f"缺少必需列: {missing_cols}。")
    
    if date_column and date_column not in df.columns:
        messages.append(f"缺少指定的日期列: {date_column}。")
    elif date_column and date_column in df.columns and not pd.api.types.is_datetime64_any_dtype(df[date_column]):
        # Added check 'date_column in df.columns' before accessing df[date_column]
        messages.append(f"日期列 '{date_column}' 不是datetime类型。")

    if check_na:
        cols_to_check_na = required_columns if required_columns else df.columns
        valid_cols_to_check_na = [col for col in cols_to_check_na if col in df.columns]

        if valid_cols_to_check_na : # Ensure there are columns to check
            na_counts = df[valid_cols_to_check_na].isnull().sum()
            cols_with_na = na_counts[na_counts > 0]
            if not cols_with_na.empty:
                for col, count in cols_with_na.items():
                    messages.append(f"列 '{col}' 包含 {count} 个空值。")
    
    if messages: 
        return False, messages
    return True, ["数据验证通过。"]


def analyze_trade_calendar_stats(start_year=None, end_year=None):
    """
    分析交易日历的统计信息，返回统计字典。
    """
    tdays, _ = _ensure_calendar_loaded()
    if not tdays:
        warnings.warn("交易日历未加载，无法分析。")
        return None
    
    trade_df = pd.DataFrame({
        'date': tdays,
        'year': [d.year for d in tdays],
        'month': [d.month for d in tdays],
        'weekday': [d.weekday() for d in tdays]
    })
    
    min_cal_year, max_cal_year = trade_df['year'].min(), trade_df['year'].max()
    s_year = start_year if start_year is not None else min_cal_year
    e_year = end_year if end_year is not None else max_cal_year
    
    trade_df = trade_df[(trade_df['year'] >= s_year) & (trade_df['year'] <= e_year)]
    if trade_df.empty:
        warnings.warn(f"在年份 {s_year}-{e_year} 范围无交易日数据。")
        return None

    yearly_counts = trade_df.groupby('year').size()
    monthly_avg_counts = trade_df.groupby(['year', 'month']).size().groupby('month').mean()
    weekday_dist = trade_df.groupby('weekday').size().reindex(range(7), fill_value=0) 
    
    all_dates_in_range = pd.date_range(start=trade_df['date'].min(), end=trade_df['date'].max())
    df_cont = pd.DataFrame({'date': all_dates_in_range})
    df_cont['is_trade_day'] = df_cont['date'].isin(_trade_dates_set) 
    df_cont['group'] = (df_cont['is_trade_day'] != df_cont['is_trade_day'].shift()).cumsum()
    
    period_lengths = df_cont.groupby(['group', 'is_trade_day']).size()
    trade_periods = period_lengths[period_lengths.index.get_level_values('is_trade_day') == True]
    non_trade_periods = period_lengths[period_lengths.index.get_level_values('is_trade_day') == False]

    stats = {
        'analysis_period': f"{trade_df['year'].min()}-{trade_df['year'].max()}",
        'total_trade_days': len(trade_df),
        'avg_trade_days_per_year': yearly_counts.mean(),
        'std_trade_days_per_year': yearly_counts.std(),
        'trade_days_by_year': yearly_counts.to_dict(),
        'avg_trade_days_by_month': monthly_avg_counts.to_dict(),
        'trade_days_by_weekday': {
            i: {'count': weekday_dist.get(i,0), 
                'percent': round(weekday_dist.get(i,0) / len(trade_df) * 100,1) if len(trade_df)>0 else 0}
            for i in range(7) 
        },
        'max_continuous_trade_days': trade_periods.max() if not trade_periods.empty else 0,
        'max_continuous_non_trade_days': non_trade_periods.max() if not non_trade_periods.empty else 0,
        'holidays_gt_3_days_count': len(non_trade_periods[non_trade_periods >= 3])
    }
    return stats

def load_and_preprocess_all_market_data(file_path):
    """
    Loads and performs initial preprocessing on the full market Parquet data file.
    - Renames columns to English standards.
    - Converts 'Date' column to datetime objects.
    - Sets 'Date' as the index.
    """
    print(f"Loading and preprocessing data from {file_path}...")
    try:
        df = pd.read_parquet(file_path)
        
        # Define mappings for robustness
        column_mapping = {
            '日期': 'Date', '股票代码': 'StockCode', '开盘': 'Open',
            '最高': 'High', '最低': 'Low', '收盘': 'Close', '成交量': 'Volume'
        }
        
        # Rename columns that exist in the DataFrame
        df.rename(columns={k: v for k, v in column_mapping.items() if k in df.columns}, inplace=True)
        
        if 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'])
            df.set_index('Date', inplace=True)
        else:
            warnings.warn("'Date' column ('日期') not found in the data. Index not set.")
            
        print("Data loaded and preprocessed successfully.")
        return df
        
    except FileNotFoundError:
        print(f"Error: Data file not found at {file_path}")
        return None
    except Exception as e:
        print(f"An error occurred while loading or preprocessing data: {e}")
        return None


def load_stock_name_mapping(project_root):
    """
    Loads the full stock code to stock name mapping from the dedicated CSV file.
    """
    name_map = {}
    market_prefixes = {'sh': 'sh_stock_list.csv', 'sz': 'sz_stock_list.csv', 'bj': 'bj_stock_list.csv'}

    for prefix, filename in market_prefixes.items():
        file_path = os.path.join(project_root, 'ashare', filename)
        try:
            df = pd.read_csv(file_path, usecols=['证券代码', '证券简称'], dtype={'证券代码': str})
            # Ensure '证券代码' is padded with leading zeros for 6-digit codes if necessary, common for SZ/SH.
            if prefix in ['sh', 'sz']:
                df['证券代码'] = df['证券代码'].str.zfill(6)

            df.rename(columns={'证券简称': 'StockName'}, inplace=True)
            df['StockCode'] = prefix + '.' + df['证券代码']
            
            # Update the main map, newer entries can overwrite older ones if duplicates exist
            name_map.update(pd.Series(df.StockName.values, index=df.StockCode).to_dict())
        except FileNotFoundError:
            print(f"Warning: Stock list file not found at {file_path}, skipping.")
        except ValueError:
            # This can happen if one of the required columns is not in the CSV
            print(f"Warning: '证券代码' or '证券简称' not in {file_path}, skipping.")
        except Exception as e:
            print(f"An error occurred while processing {file_path}: {e}")
            
    return name_map

def load_hk_stock_name_mapping(project_root):
    """
    Loads Hong Kong stock code to stock name mapping from individual CSV files.
    Each file in data/H_daily/ contains code and name columns.
    """
    import glob

    name_map = {}
    h_daily_path = os.path.join(project_root, 'data', 'H_daily')

    if not os.path.exists(h_daily_path):
        print(f"Warning: Hong Kong data directory not found at {h_daily_path}")
        return name_map

    # Get all CSV files in H_daily directory
    csv_files = glob.glob(os.path.join(h_daily_path, '*.csv'))

    for file_path in csv_files:
        try:
            # Read only the first row of data (header + first data row)
            df = pd.read_csv(file_path, nrows=1, usecols=['code', 'name'])

            if not df.empty:
                stock_code = df.iloc[0]['code']
                stock_name = df.iloc[0]['name']
                name_map[stock_code] = stock_name

        except Exception as e:
            # Skip files that can't be read or don't have the expected columns
            continue

    print(f"Loaded {len(name_map)} Hong Kong stock names")
    return name_map

def define_market_regime(index_df: pd.DataFrame, sma_period: int = 200, buffer: float = 0.05) -> pd.DataFrame:
    """
    Defines market regimes (Bull, Bear, Consolidation) based on a long-term SMA.
    """
    df = index_df.copy()
    df['SMA'] = df['Close'].rolling(window=sma_period).mean()
    
    # Define conditions for each regime
    bull_condition = df['Close'] > df['SMA'] * (1 + buffer)
    bear_condition = df['Close'] < df['SMA'] * (1 - buffer)
    
    # Assign regime based on conditions
    df['Regime'] = '震荡市 (Consolidation)'
    df.loc[bull_condition, 'Regime'] = '牛市 (Bull)'
    df.loc[bear_condition, 'Regime'] = '熊市 (Bear)'
    
    return df[['Regime']]