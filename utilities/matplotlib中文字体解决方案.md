# Matplotlib中文字体显示问题解决方案

## 问题描述

在使用Matplotlib绘制图表时，当图表中包含中文字符（如标题、轴标签等）时，默认情况下会显示为乱码或方框。这是因为Matplotlib默认使用的字体不支持中文字符。

## 解决方案

我们提供了三种解决方案，从简单到复杂：

### 方案一：直接使用系统中文字体（推荐）

```python
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.font_manager import FontProperties

def setup_chinese_font():
    """设置中文字体，返回字体属性对象"""
    # 获取系统中可用的中文字体
    cn_fonts = []
    for font in fm.fontManager.ttflist:
        if any(name in font.name for name in ['Ping', 'ping', 'Hei', 'hei', 'Song', 'Ming', 'Yuan']):
            cn_fonts.append(font.name)
    
    # 优先选择的字体列表
    preferred_fonts = ['SimHei', 'Microsoft YaHei', 'PingFang SC', 'STHeiti', 'SimSun']
    
    # 查找最优字体
    best_font = None
    for font in preferred_fonts:
        if font in cn_fonts:
            best_font = font
            break
    
    # 如果没有找到优先字体，使用任何可用的中文字体
    if not best_font and cn_fonts:
        best_font = cn_fonts[0]
    
    # 如果找不到任何中文字体，使用默认字体
    if not best_font:
        print("警告：找不到中文字体，将使用默认字体")
        best_font = 'sans-serif'
    
    # 设置全局字体
    plt.rcParams['font.sans-serif'] = [best_font] + plt.rcParams['font.sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建字体属性对象以供后续使用
    return FontProperties(family=best_font)

# 使用示例
font_prop = setup_chinese_font()

# 创建图表
plt.figure(figsize=(10, 6))
plt.title('这是中文标题', fontproperties=font_prop)
plt.xlabel('横轴', fontproperties=font_prop)
plt.ylabel('纵轴', fontproperties=font_prop)
plt.plot([1, 2, 3, 4], [1, 4, 9, 16])
plt.savefig('中文图表.png')
plt.close()
```

### 方案二：为每个文本元素单独设置字体

如果方案一不起作用，可以尝试为每个文本元素单独设置字体：

```python
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 查找系统中的中文字体
chinese_fonts = [f.name for f in fm.fontManager.ttflist 
                if any(name in f.name for name in ['Hei', 'Song', 'Ming', 'Yuan', 'Ping'])]
if chinese_fonts:
    font_name = chinese_fonts[0]
    font = fm.FontProperties(family=font_name)
    
    plt.figure(figsize=(10, 6))
    plt.title('这是中文标题', fontproperties=font)
    plt.xlabel('横轴', fontproperties=font)
    plt.ylabel('纵轴', fontproperties=font)
    plt.plot([1, 2, 3, 4], [1, 4, 9, 16])
    
    # 如果有图例，也需要设置字体
    plt.legend(['数据'], prop=font)
    
    plt.savefig('中文图表.png')
    plt.close()
else:
    print("系统中没有找到中文字体！")
```

### 方案三：完整解决方案（适用于复杂项目）

对于复杂项目，我们提供了一个完整的解决方案，包括：
1. 检测系统中可用的中文字体
2. 自动选择最佳字体
3. 应用字体到所有图表元素
4. 处理特殊情况（如子图、图例等）

完整代码请参考 `complete_font_solution.py` 文件。

## 系统特定解决方案

### Windows系统

Windows系统通常已安装中文字体，可以直接使用：

```python
plt.rcParams['font.sans-serif'] = ['SimHei']  # 黑体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题
```

### macOS系统

macOS系统可以使用：

```python
plt.rcParams['font.sans-serif'] = ['PingFang SC', 'STHeiti']
plt.rcParams['axes.unicode_minus'] = False
```

### Linux系统

Linux系统可能需要先安装中文字体：

```bash
# Ubuntu/Debian
sudo apt-get install fonts-wqy-zenhei

# CentOS/RHEL
sudo yum install wqy-zenhei-fonts
```

然后在Python中：

```python
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei']
plt.rcParams['axes.unicode_minus'] = False
```

## 常见问题

1. **仍然显示乱码**：确保选择的字体真的支持中文字符。可以通过`check_system_chinese_fonts()`函数检查系统中可用的中文字体。

2. **负号显示为方框**：设置`plt.rcParams['axes.unicode_minus'] = False`可以解决。

3. **子图或特殊元素显示乱码**：确保为所有文本元素（包括子图标题、图例文本等）都设置了字体。

4. **字体警告信息**：可以通过以下方式忽略警告：
   ```python
   import warnings
   warnings.filterwarnings("ignore", category=UserWarning, module="matplotlib")
   ```

## 总结

解决Matplotlib中文字体问题的关键是找到系统中可用的中文字体，并正确应用到图表的各个文本元素上。本文提供的解决方案已经在多个系统上测试通过，应该能够解决大多数中文显示问题。 