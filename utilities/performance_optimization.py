# performance_optimization.py
"""
Performance optimization utilities for trading strategies.
Provides caching, memory optimization, and batch processing capabilities.
"""

import os
import sys
import gc
import psutil
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from concurrent.futures import <PERSON><PERSON><PERSON><PERSON>xecutor, ThreadPoolExecutor, as_completed
import warnings

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    import numba
    from numba import njit, prange
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    warnings.warn("Numba not available, some optimizations will be disabled")

class MemoryMonitor:
    """Monitor and optimize memory usage during batch processing."""
    
    def __init__(self, warning_threshold: float = 0.8, critical_threshold: float = 0.9):
        """
        Initialize memory monitor.
        
        Args:
            warning_threshold: Memory usage percentage to trigger warning
            critical_threshold: Memory usage percentage to trigger cleanup
        """
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.process = psutil.Process()
        
    def get_memory_usage(self) -> float:
        """Get current memory usage as percentage of total system memory."""
        memory_info = self.process.memory_info()
        system_memory = psutil.virtual_memory().total
        return memory_info.rss / system_memory
    
    def check_memory_and_cleanup(self, force_cleanup: bool = False) -> bool:
        """
        Check memory usage and perform cleanup if necessary.
        
        Args:
            force_cleanup: Force garbage collection regardless of memory usage
            
        Returns:
            True if cleanup was performed
        """
        memory_usage = self.get_memory_usage()
        
        if force_cleanup or memory_usage > self.critical_threshold:
            gc.collect()
            return True
        elif memory_usage > self.warning_threshold:
            warnings.warn(f"High memory usage: {memory_usage:.1%}")
            
        return False
    
    def get_memory_info(self) -> Dict[str, Any]:
        """Get detailed memory information."""
        memory_info = self.process.memory_info()
        system_memory = psutil.virtual_memory()
        
        return {
            'process_memory_mb': memory_info.rss / (1024 * 1024),
            'process_memory_pct': memory_info.rss / system_memory.total,
            'system_memory_available_mb': system_memory.available / (1024 * 1024),
            'system_memory_used_pct': system_memory.percent / 100
        }

class DataFrameOptimizer:
    """Optimize DataFrame memory usage and operations."""
    
    @staticmethod
    def optimize_dtypes(df: pd.DataFrame, preserve_price_precision: bool = True) -> pd.DataFrame:
        """
        Optimize DataFrame data types to reduce memory usage.
        
        Args:
            df: DataFrame to optimize
            preserve_price_precision: Keep price columns as float32
            
        Returns:
            Optimized DataFrame
        """
        df_optimized = df.copy()
        
        # Price columns that should maintain precision
        price_columns = ['Open', 'High', 'Low', 'Close', 'open', 'high', 'low', 'close',
                        '开盘', '最高', '最低', '收盘']
        
        for col in df_optimized.select_dtypes(include=[np.number]).columns:
            if preserve_price_precision and col in price_columns:
                # Keep price columns as float32 for precision
                if df_optimized[col].dtype == 'float64':
                    df_optimized[col] = df_optimized[col].astype(np.float32)
            else:
                # Try to downcast other numeric columns
                if df_optimized[col].dtype == 'float64':
                    df_optimized[col] = pd.to_numeric(df_optimized[col], downcast='float')
                elif df_optimized[col].dtype in ['int64', 'int32']:
                    df_optimized[col] = pd.to_numeric(df_optimized[col], downcast='integer')
        
        return df_optimized
    
    @staticmethod
    def get_memory_usage(df: pd.DataFrame) -> Dict[str, float]:
        """Get DataFrame memory usage information."""
        memory_usage = df.memory_usage(deep=True)
        total_mb = memory_usage.sum() / (1024 * 1024)
        
        return {
            'total_mb': total_mb,
            'per_column_mb': {col: usage / (1024 * 1024) 
                            for col, usage in memory_usage.items()},
            'shape': df.shape
        }

class BatchProcessor:
    """Efficient batch processing for large datasets."""
    
    def __init__(self, max_workers: int = None, use_multiprocessing: bool = True,
                 chunk_size: int = 100, memory_monitor: MemoryMonitor = None):
        """
        Initialize batch processor.
        
        Args:
            max_workers: Maximum number of parallel workers
            use_multiprocessing: Use ProcessPoolExecutor vs ThreadPoolExecutor
            chunk_size: Number of items to process in each chunk
            memory_monitor: Memory monitoring instance
        """
        self.max_workers = max_workers or min(os.cpu_count() or 1, 8)
        self.use_multiprocessing = use_multiprocessing
        self.chunk_size = chunk_size
        self.memory_monitor = memory_monitor or MemoryMonitor()
        
    def process_in_chunks(self, items: List[Any], process_func: callable,
                         progress_callback: callable = None) -> List[Any]:
        """
        Process items in chunks with parallel execution.
        
        Args:
            items: List of items to process
            process_func: Function to process each item
            progress_callback: Optional callback for progress updates
            
        Returns:
            List of processed results
        """
        # Split items into chunks
        chunks = [items[i:i + self.chunk_size] 
                 for i in range(0, len(items), self.chunk_size)]
        
        all_results = []
        ExecutorClass = ProcessPoolExecutor if self.use_multiprocessing else ThreadPoolExecutor
        
        for chunk_idx, chunk in enumerate(chunks):
            if progress_callback:
                progress_callback(f"Processing chunk {chunk_idx + 1}/{len(chunks)} ({len(chunk)} items)")
            
            with ExecutorClass(max_workers=self.max_workers) as executor:
                # Submit tasks for current chunk
                futures = [executor.submit(process_func, item) for item in chunk]
                
                # Collect results
                chunk_results = []
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        chunk_results.append(result)
                    except Exception as e:
                        warnings.warn(f"Error processing item: {e}")
                        chunk_results.append(None)
                
                all_results.extend(chunk_results)
            
            # Memory cleanup after each chunk
            self.memory_monitor.check_memory_and_cleanup()
        
        return all_results

if NUMBA_AVAILABLE:
    @njit(fastmath=True)
    def fast_rolling_window_numba(arr: np.ndarray, window: int, 
                                 operation: str = 'sum') -> np.ndarray:
        """
        Fast rolling window operations using Numba.
        
        Args:
            arr: Input array
            window: Window size
            operation: Operation type ('sum', 'mean', 'max', 'min')
            
        Returns:
            Array with rolling window results
        """
        n = len(arr)
        result = np.full(n, np.nan)
        
        for i in range(window - 1, n):
            window_data = arr[i - window + 1:i + 1]
            
            if operation == 'sum':
                result[i] = np.sum(window_data)
            elif operation == 'mean':
                result[i] = np.mean(window_data)
            elif operation == 'max':
                result[i] = np.max(window_data)
            elif operation == 'min':
                result[i] = np.min(window_data)
        
        return result

else:
    def fast_rolling_window_numba(arr: np.ndarray, window: int, 
                                 operation: str = 'sum') -> np.ndarray:
        """Fallback rolling window without Numba."""
        series = pd.Series(arr)
        
        if operation == 'sum':
            return series.rolling(window=window, min_periods=window).sum().values
        elif operation == 'mean':
            return series.rolling(window=window, min_periods=window).mean().values
        elif operation == 'max':
            return series.rolling(window=window, min_periods=window).max().values
        elif operation == 'min':
            return series.rolling(window=window, min_periods=window).min().values
        else:
            raise ValueError(f"Unsupported operation: {operation}")

def optimize_market_data_loading(file_path: str, date_column: str = '日期',
                               start_date: str = None, end_date: str = None,
                               stock_codes: List[str] = None) -> pd.DataFrame:
    """
    Optimized market data loading with filtering and memory optimization.

    Args:
        file_path: Path to market data file
        date_column: Name of date column
        start_date: Start date filter (YYYY-MM-DD)
        end_date: End date filter (YYYY-MM-DD)
        stock_codes: List of stock codes to filter

    Returns:
        Optimized DataFrame
    """
    # Load data with optimized dtypes
    if file_path.endswith('.parquet'):
        df = pd.read_parquet(file_path)
    elif file_path.endswith('.csv'):
        df = pd.read_csv(file_path)
    else:
        raise ValueError("Unsupported file format")

    # Convert date column
    if date_column in df.columns:
        df[date_column] = pd.to_datetime(df[date_column])

    # Apply filters
    if start_date:
        df = df[df[date_column] >= start_date]
    if end_date:
        df = df[df[date_column] <= end_date]
    if stock_codes:
        # Try different possible column names for stock codes
        possible_code_columns = ['股票代码', '代码', 'code', 'stock_code', 'StockCode']
        code_column = None
        for col in possible_code_columns:
            if col in df.columns:
                code_column = col
                break

        if code_column:
            df = df[df[code_column].isin(stock_codes)]
        else:
            raise ValueError(f"Could not find stock code column. Available columns: {df.columns.tolist()}")

    # Optimize memory usage
    df = DataFrameOptimizer.optimize_dtypes(df)

    return df

# Example usage and testing
if __name__ == "__main__":
    # Test memory monitor
    monitor = MemoryMonitor()
    print("Memory info:", monitor.get_memory_info())
    
    # Test DataFrame optimizer
    test_df = pd.DataFrame({
        'Open': np.random.random(1000).astype(np.float64),
        'High': np.random.random(1000).astype(np.float64),
        'Low': np.random.random(1000).astype(np.float64),
        'Close': np.random.random(1000).astype(np.float64),
        'Volume': np.random.randint(1000, 10000, 1000).astype(np.int64)
    })
    
    print("Original memory usage:", DataFrameOptimizer.get_memory_usage(test_df))
    
    optimized_df = DataFrameOptimizer.optimize_dtypes(test_df)
    print("Optimized memory usage:", DataFrameOptimizer.get_memory_usage(optimized_df))
