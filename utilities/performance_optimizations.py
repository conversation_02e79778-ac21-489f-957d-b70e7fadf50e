# performance_optimizations.py
# 性能优化工具集

import pandas as pd
import numpy as np
from functools import lru_cache
import gc
from typing import Dict, List, Tuple

class MemoryOptimizedDataLoader:
    """内存优化的数据加载器"""
    
    def __init__(self, chunk_size: int = 1000):
        self.chunk_size = chunk_size
        self._cache = {}
    
    def load_market_data_chunked(self, file_path: str, stock_codes: List[str] = None):
        """分块加载市场数据，减少内存占用"""
        try:
            # 使用更高效的数据类型
            dtype_dict = {
                'StockCode': 'category',
                'Open': 'float32',
                'High': 'float32', 
                'Low': 'float32',
                'Close': 'float32',
                'Volume': 'int32'
            }
            
            if stock_codes:
                # 只加载需要的股票数据
                df = pd.read_parquet(file_path, 
                                   filters=[('StockCode', 'in', stock_codes)])
            else:
                df = pd.read_parquet(file_path)
            
            # 转换数据类型以节省内存
            for col, dtype in dtype_dict.items():
                if col in df.columns:
                    df[col] = df[col].astype(dtype)
            
            return df
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return None
    
    @lru_cache(maxsize=128)
    def get_stock_data_cached(self, stock_code: str, start_date: str, end_date: str):
        """缓存股票数据以避免重复加载"""
        # 实现缓存逻辑
        pass

class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.timings = {}
        self.memory_usage = {}
    
    def profile_function(self, func_name: str):
        """装饰器：分析函数性能"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                import time
                import psutil
                import os
                
                # 记录开始时间和内存
                start_time = time.time()
                process = psutil.Process(os.getpid())
                start_memory = process.memory_info().rss / 1024 / 1024  # MB
                
                # 执行函数
                result = func(*args, **kwargs)
                
                # 记录结束时间和内存
                end_time = time.time()
                end_memory = process.memory_info().rss / 1024 / 1024  # MB
                
                # 保存性能数据
                self.timings[func_name] = end_time - start_time
                self.memory_usage[func_name] = end_memory - start_memory
                
                print(f"{func_name}: {self.timings[func_name]:.2f}s, "
                      f"内存变化: {self.memory_usage[func_name]:.2f}MB")
                
                return result
            return wrapper
        return decorator
    
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        return {
            'timings': self.timings,
            'memory_usage': self.memory_usage,
            'total_time': sum(self.timings.values()),
            'total_memory': sum(self.memory_usage.values())
        }

def optimize_dataframe_memory(df: pd.DataFrame, preserve_price_precision: bool = True) -> pd.DataFrame:
    """
    优化DataFrame内存使用

    Args:
        df: 要优化的DataFrame
        preserve_price_precision: 是否保持价格数据的精度（避免float64->float32转换）
    """
    original_memory = df.memory_usage(deep=True).sum() / 1024**2

    # 定义需要保持高精度的列（价格相关）
    price_columns = {'Open', 'High', 'Low', 'Close', 'open', 'high', 'low', 'close',
                    'price', 'Price', '开盘', '最高', '最低', '收盘'}

    for col in df.columns:
        col_type = df[col].dtype

        if col_type != 'object':
            c_min = df[col].min()
            c_max = df[col].max()

            # 跳过包含NaN的列
            if pd.isna(c_min) or pd.isna(c_max):
                continue

            if str(col_type)[:3] == 'int':
                # 整数类型优化
                if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:
                    df[col] = df[col].astype(np.int8)
                elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:
                    df[col] = df[col].astype(np.int16)
                elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:
                    df[col] = df[col].astype(np.int32)

            elif str(col_type)[:5] == 'float':
                # 浮点数类型优化 - 但保护价格列
                if preserve_price_precision and col in price_columns:
                    # 保持价格列为float64以确保精度
                    continue
                else:
                    # 非价格列可以安全转换为float32
                    if c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:
                        df[col] = df[col].astype(np.float32)

        # 字符串列优化为category（如果重复值较多）
        elif col_type == 'object' and col in {'StockCode', 'stock_code', '股票代码'}:
            if df[col].nunique() / len(df) < 0.5:  # 如果唯一值比例小于50%
                df[col] = df[col].astype('category')

    optimized_memory = df.memory_usage(deep=True).sum() / 1024**2
    print(f"内存优化: {original_memory:.2f}MB -> {optimized_memory:.2f}MB "
          f"(节省 {100 * (original_memory - optimized_memory) / original_memory:.1f}%)")

    return df

def batch_process_stocks(stock_list: List[str], process_func, batch_size: int = 100):
    """批量处理股票以控制内存使用"""
    results = []
    
    for i in range(0, len(stock_list), batch_size):
        batch = stock_list[i:i + batch_size]
        print(f"处理批次 {i//batch_size + 1}/{(len(stock_list)-1)//batch_size + 1}")
        
        batch_results = process_func(batch)
        results.extend(batch_results)
        
        # 强制垃圾回收
        gc.collect()
    
    return results

# 使用示例
if __name__ == "__main__":
    # 性能分析示例
    profiler = PerformanceProfiler()
    
    @profiler.profile_function("test_function")
    def test_function():
        import time
        time.sleep(1)
        return "完成"
    
    result = test_function()
    report = profiler.get_performance_report()
    print("性能报告:", report)
