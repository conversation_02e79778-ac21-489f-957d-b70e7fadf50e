import pandas as pd
import numpy as np
from tqdm import tqdm

class Portfolio:
    def __init__(self, initial_capital=1_000_000, commission_rate=0.0003, stamp_duty_rate=0.0005, position_sizing_fn=None):
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.commission_rate = commission_rate
        self.stamp_duty_rate = stamp_duty_rate
        self.positions = {}  # {'stock_code': {'shares': X, 'entry_price': Y, 'entry_date': Z}}
        self.history = []
        self.trade_log = [] # To store details of each completed trade
        self.position_sizing_fn = position_sizing_fn if position_sizing_fn else self._default_position_sizer

    def _default_position_sizer(self, total_nav, price_per_share, stock_code=None):
        return total_nav * 0.05

    def get_nav(self, current_prices_dict):
        positions_value = 0
        for stock, details in self.positions.items():
            current_price = current_prices_dict.get(stock, details['entry_price'])
            positions_value += details['shares'] * current_price
        return self.cash + positions_value, positions_value

    def record_history(self, date, current_prices_dict):
        nav, positions_value = self.get_nav(current_prices_dict)
        self.history.append({
            'Date': date,
            'NAV': nav,
            'Cash': self.cash,
            'PositionsValue': positions_value,
            'Positions': self.positions.copy()
        })

    def execute_buy(self, stock, date, price, current_nav):
        if stock in self.positions:
            return False

        target_position_value = self.position_sizing_fn(current_nav, price, stock_code=stock, entry_date=date)
        shares_to_buy = int(target_position_value / price / 100) * 100

        if shares_to_buy == 0:
            return False

        cost_of_shares = shares_to_buy * price
        commission = cost_of_shares * self.commission_rate
        total_cost = cost_of_shares + commission

        if self.cash < total_cost:
            affordable_value = self.cash / (1 + self.commission_rate) * 0.999 
            shares_to_buy = int(affordable_value / price / 100) * 100
            if shares_to_buy == 0:
                return False
            cost_of_shares = shares_to_buy * price
            commission = cost_of_shares * self.commission_rate
            total_cost = cost_of_shares + commission
            if self.cash < total_cost:
                return False

        self.cash -= total_cost
        self.positions[stock] = {'shares': shares_to_buy, 'entry_price': price, 'entry_date': date}
        return True

    def execute_sell(self, stock, date, price, exit_reason="Not Specified"):
        if stock not in self.positions:
            return False

        details = self.positions[stock]
        shares_to_sell = details['shares']
        sale_value = shares_to_sell * price
        commission = sale_value * self.commission_rate
        stamp_duty = sale_value * self.stamp_duty_rate
        total_fees = commission + stamp_duty

        self.cash += sale_value - total_fees
        
        pnl = (price - details['entry_price']) * shares_to_sell - total_fees

        self.trade_log.append({
            'StockCode': stock,
            'EntryDate': details['entry_date'],
            'EntryPrice': details['entry_price'],
            'Shares': shares_to_sell,
            'ExitDate': date,
            'ExitPrice': price,
            'ExitReason': exit_reason,
            'PnL': pnl
        })

        del self.positions[stock]
        return True

def add_stock_code_prefix(df):
    """Adds 'sh.' or 'sz.' prefix to stock codes based on their first digits."""
    def get_prefix(code):
        if isinstance(code, str):
            if code.startswith('60') or code.startswith('688'):
                return f"sh.{code}"
            elif code.startswith('00') or code.startswith('300'):
                return f"sz.{code}"
            elif code.startswith('8') or code.startswith('43'):
                return f"bj.{code}"
        return code

    if 'StockCode' in df.columns:
        df['StockCode'] = df['StockCode'].astype(str).str.zfill(6)
        df['StockCode'] = df['StockCode'].apply(get_prefix)
    return df

def run_portfolio_simulation(
    trades_df,
    market_data_df,
    start_date,
    initial_capital,
    commission_rate=0.0003,
    stamp_duty_rate=0.0005,
    position_sizing_fn=None
):
    """
    Runs a deterministic portfolio backtest. Assumes trades_df is pre-filtered.
    """
    trades_df = trades_df.copy()
    
    print("Preparing data for deterministic simulation...")
    trades_df['EntryDate'] = pd.to_datetime(trades_df['EntryDate'])
    trades_df['ExitDate'] = pd.to_datetime(trades_df['ExitDate'])
    trades_df = trades_df[trades_df['EntryDate'] >= pd.to_datetime(start_date)]

    _market_data_df = market_data_df.copy()
    if 'Date' not in _market_data_df.columns:
        if 'Date' in _market_data_df.index.names:
            _market_data_df.reset_index(inplace=True)
        else:
            raise KeyError("'market_data_df' must have a 'Date' column or index.")

    _market_data_df['Date'] = pd.to_datetime(_market_data_df['Date'])
    _market_data_df = _market_data_df[_market_data_df['Date'] >= pd.to_datetime(start_date)]

    trades_df = pd.merge(trades_df, _market_data_df[['Date', 'StockCode', 'Close']],
                         left_on=['EntryDate', 'StockCode'], right_on=['Date', 'StockCode'], how='left')\
                         .rename(columns={'Close': 'ActualEntryPrice', 'Date_x': 'Date'}).drop(columns=['Date_y'], errors='ignore')

    trades_df = pd.merge(trades_df, _market_data_df[['Date', 'StockCode', 'Close']],
                         left_on=['ExitDate', 'StockCode'], right_on=['Date', 'StockCode'], how='left')\
                         .rename(columns={'Close': 'ActualExitPrice', 'Date_x': 'Date'}).drop(columns=['Date_y'], errors='ignore')

    trades_df.dropna(subset=['ActualEntryPrice'], inplace=True)
    trades_df['ActualExitPrice'] = trades_df['ActualExitPrice'].fillna(-1)

    buy_events = trades_df[['EntryDate', 'StockCode', 'ActualEntryPrice']].copy()
    buy_events.rename(columns={'EntryDate': 'Date', 'ActualEntryPrice': 'Price'}, inplace=True)
    buy_events['Type'] = 'BUY'
    
    if 'ExitReason' not in trades_df.columns:
        trades_df['ExitReason'] = 'Not Specified'

    sell_events = trades_df[trades_df['ActualExitPrice'] != -1][['ExitDate', 'StockCode', 'ActualExitPrice', 'ExitReason']].copy()
    sell_events.rename(columns={'ExitDate': 'Date', 'ActualExitPrice': 'Price'}, inplace=True)
    sell_events['Type'] = 'SELL'

    events = pd.concat([buy_events, sell_events]).sort_values(by='Date').set_index('Date')
    
    if events.empty:
        print("No events to process after filtering.")
        empty_trades = pd.DataFrame(columns=['StockCode', 'EntryDate', 'EntryPrice', 'Shares', 'ExitDate', 'ExitPrice', 'ExitReason', 'PnL'])
        empty_history = pd.DataFrame(columns=['Date', 'NAV', 'Cash', 'PositionsValue', 'Positions']).set_index('Date')
        return empty_history, empty_trades

    print("Starting portfolio simulation...")
    portfolio = Portfolio(initial_capital=initial_capital, commission_rate=commission_rate, stamp_duty_rate=stamp_duty_rate, position_sizing_fn=position_sizing_fn)
    
    daily_prices_pivot = _market_data_df.pivot_table(index='Date', columns='StockCode', values='Close')
    daily_prices_pivot.ffill(inplace=True)

    all_sim_dates = daily_prices_pivot.index[daily_prices_pivot.index >= events.index.min()]

    last_recorded_date = pd.Timestamp.min 

    for current_date in tqdm(all_sim_dates, desc="Simulating"):
        current_prices_for_held_stocks = {stock: daily_prices_pivot.loc[current_date].get(stock, details['entry_price']) for stock, details in portfolio.positions.items()}
        
        if current_date > last_recorded_date:
            portfolio.record_history(current_date, current_prices_for_held_stocks)
            last_recorded_date = current_date

        if current_date in events.index:
            daily_events = events.loc[current_date]
            if isinstance(daily_events, pd.Series): daily_events = pd.DataFrame([daily_events])
            
            sells_today = daily_events[daily_events['Type'] == 'SELL']
            for _, trade in sells_today.iterrows():
                portfolio.execute_sell(trade['StockCode'], current_date, trade['Price'], trade.get('ExitReason', 'Not Specified'))
            
            current_nav_before_buys, _ = portfolio.get_nav(current_prices_for_held_stocks)
            
            buys_today = daily_events[daily_events['Type'] == 'BUY']
            for _, trade in buys_today.iterrows():
                if pd.notna(trade['Price']) and trade['Price'] > 0: 
                    portfolio.execute_buy(trade['StockCode'], current_date, trade['Price'], current_nav_before_buys)

            if last_recorded_date != current_date or not sells_today.empty or not buys_today.empty:
                current_prices_for_held_stocks = {stock: daily_prices_pivot.loc[current_date].get(stock, details['entry_price']) for stock, details in portfolio.positions.items()}
                if portfolio.history and portfolio.history[-1]['Date'] == current_date:
                    portfolio.history.pop() 
                portfolio.record_history(current_date, current_prices_for_held_stocks)
                last_recorded_date = current_date

    print("Simulation finished.")
    history_df = pd.DataFrame(portfolio.history).set_index('Date')
    trade_log_df = pd.DataFrame(portfolio.trade_log)
    
    if trade_log_df.empty:
        trade_log_df = pd.DataFrame(columns=['StockCode', 'EntryDate', 'EntryPrice', 'Shares', 'ExitDate', 'ExitPrice', 'ExitReason', 'PnL'])
        
    return history_df, trade_log_df


# --- Stochastic Simulation Function ---
def run_stochastic_portfolio_simulation(
    trades_df,
    market_data_df,
    start_date,
    initial_capital,
    commission_rate=0.0003,
    stamp_duty_rate=0.0005,
    position_sizing_fn=None
):
    """
    Runs a single, stochastic portfolio backtest where trade selection on days
    with signal clusters is randomized by shuffling the order of buys.
    Assumes trades_df is pre-filtered.
    """
    trades_df = trades_df.copy()
    
    trades_df['EntryDate'] = pd.to_datetime(trades_df['EntryDate'])
    trades_df['ExitDate'] = pd.to_datetime(trades_df['ExitDate'])
    trades_df = trades_df[trades_df['EntryDate'] >= pd.to_datetime(start_date)]

    _market_data_df = market_data_df.copy()
    if 'Date' not in _market_data_df.columns:
        if 'Date' in _market_data_df.index.names:
            _market_data_df.reset_index(inplace=True)
        else:
            raise KeyError("'market_data_df' must have a 'Date' column or index.")

    _market_data_df['Date'] = pd.to_datetime(_market_data_df['Date'])
    _market_data_df = _market_data_df[_market_data_df['Date'] >= pd.to_datetime(start_date)]

    trades_df = pd.merge(trades_df, _market_data_df[['Date', 'StockCode', 'Close']],
                         left_on=['EntryDate', 'StockCode'], right_on=['Date', 'StockCode'], how='left')\
                         .rename(columns={'Close': 'ActualEntryPrice', 'Date_x': 'Date'}).drop(columns=['Date_y'], errors='ignore')

    trades_df = pd.merge(trades_df, _market_data_df[['Date', 'StockCode', 'Close']],
                         left_on=['ExitDate', 'StockCode'], right_on=['Date', 'StockCode'], how='left')\
                         .rename(columns={'Close': 'ActualExitPrice', 'Date_x': 'Date'}).drop(columns=['Date_y'], errors='ignore')

    trades_df.dropna(subset=['ActualEntryPrice'], inplace=True)
    trades_df['ActualExitPrice'] = trades_df['ActualExitPrice'].fillna(-1)

    buy_events = trades_df[['EntryDate', 'StockCode', 'ActualEntryPrice']].copy()
    buy_events.rename(columns={'EntryDate': 'Date', 'ActualEntryPrice': 'Price'}, inplace=True)
    buy_events['Type'] = 'BUY'
    
    if 'ExitReason' not in trades_df.columns:
        trades_df['ExitReason'] = 'Not Specified'

    sell_events = trades_df[trades_df['ActualExitPrice'] != -1][['ExitDate', 'StockCode', 'ActualExitPrice', 'ExitReason']].copy()
    sell_events.rename(columns={'ExitDate': 'Date', 'ActualExitPrice': 'Price'}, inplace=True)
    sell_events['Type'] = 'SELL'

    events = pd.concat([buy_events, sell_events]).sort_values(by='Date').set_index('Date')
    
    if events.empty:
        empty_trades = pd.DataFrame(columns=['StockCode', 'EntryDate', 'EntryPrice', 'Shares', 'ExitDate', 'ExitPrice', 'ExitReason', 'PnL'])
        empty_history = pd.DataFrame(columns=['Date', 'NAV', 'Cash', 'PositionsValue', 'Positions']).set_index('Date')
        return empty_history, empty_trades

    portfolio = Portfolio(initial_capital=initial_capital, commission_rate=commission_rate, stamp_duty_rate=stamp_duty_rate, position_sizing_fn=position_sizing_fn)
    
    daily_prices_pivot = _market_data_df.pivot_table(index='Date', columns='StockCode', values='Close')
    daily_prices_pivot.ffill(inplace=True)

    all_sim_dates = daily_prices_pivot.index[daily_prices_pivot.index >= events.index.min()]

    last_recorded_date = pd.Timestamp.min 

    for current_date in tqdm(all_sim_dates, desc="Simulating Path", leave=False):
        current_prices_for_held_stocks = {stock: daily_prices_pivot.loc[current_date].get(stock, details['entry_price']) for stock, details in portfolio.positions.items()}
        
        if current_date > last_recorded_date:
            portfolio.record_history(current_date, current_prices_for_held_stocks)
            last_recorded_date = current_date

        if current_date in events.index:
            daily_events = events.loc[current_date]
            if isinstance(daily_events, pd.Series): daily_events = pd.DataFrame([daily_events])
            
            sells_today = daily_events[daily_events['Type'] == 'SELL']
            for _, trade in sells_today.iterrows():
                portfolio.execute_sell(trade['StockCode'], current_date, trade['Price'], trade.get('ExitReason', 'Not Specified'))
            
            current_nav_before_buys, _ = portfolio.get_nav(current_prices_for_held_stocks)
            
            buys_today = daily_events[daily_events['Type'] == 'BUY']
            
            # --- STOCHASTIC MODIFICATION ---
            if not buys_today.empty:
                shuffled_buys = buys_today.sample(frac=1)
                for _, trade in shuffled_buys.iterrows():
                    if pd.notna(trade['Price']) and trade['Price'] > 0: 
                        portfolio.execute_buy(trade['StockCode'], current_date, trade['Price'], current_nav_before_buys)
            # --- END OF MODIFICATION ---

            if last_recorded_date != current_date or not sells_today.empty or not buys_today.empty:
                current_prices_for_held_stocks = {stock: daily_prices_pivot.loc[current_date].get(stock, details['entry_price']) for stock, details in portfolio.positions.items()}
                if portfolio.history and portfolio.history[-1]['Date'] == current_date:
                    portfolio.history.pop() 
                portfolio.record_history(current_date, current_prices_for_held_stocks)
                last_recorded_date = current_date

    history_df = pd.DataFrame(portfolio.history).set_index('Date')
    trade_log_df = pd.DataFrame(portfolio.trade_log)
    
    if trade_log_df.empty:
        trade_log_df = pd.DataFrame(columns=['StockCode', 'EntryDate', 'EntryPrice', 'Shares', 'ExitDate', 'ExitPrice', 'ExitReason', 'PnL'])
        
    return history_df, trade_log_df
