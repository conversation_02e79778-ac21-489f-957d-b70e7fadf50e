import os
import sys
import subprocess
import platform
import pandas as pd
import numpy as np
import matplotlib
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.font_manager import FontProperties
import seaborn as sns
from datetime import datetime, timedelta
import matplotlib.dates as mdates
import warnings

# 忽略字体相关警告
warnings.filterwarnings("ignore", category=UserWarning, module="matplotlib")

def check_system_chinese_fonts():
    """
    检查系统中可用的中文字体，并返回详细信息
    """
    print("系统信息:", platform.platform())
    print("Python版本:", sys.version)
    print("Matplotlib版本:", matplotlib.__version__)
    
    # 获取系统中所有字体
    all_fonts = set([f.name for f in fm.fontManager.ttflist])
    print(f"系统中共有 {len(all_fonts)} 种字体")
    
    # 可能的中文字体关键词
    chinese_keywords = ['Hei', 'hei', 'Kai', 'kai', 'Song', 'song', 'Ming', 'ming', 
                      'Yuan', 'yuan', 'Ping', 'ping', 'Fang', 'fang', 'Yahei', 
                      'Gothic', 'SimSun', 'SimHei', 'STHeiti', 'Microsoft YaHei']
    
    # 查找可能的中文字体
    chinese_fonts = []
    for font in fm.fontManager.ttflist:
        if any(keyword in font.name for keyword in chinese_keywords):
            chinese_fonts.append((font.name, font.fname))
    
    print(f"找到 {len(chinese_fonts)} 个可能的中文字体:")
    for name, path in chinese_fonts:
        print(f"  - {name}: {path}")
    
    return chinese_fonts

def install_font_if_needed():
    """
    如果系统中没有合适的中文字体，尝试安装一个
    """
    chinese_fonts = check_system_chinese_fonts()
    
    if not chinese_fonts:
        print("系统中未找到中文字体，尝试安装...")
        
        system = platform.system()
        if system == 'Darwin':  # macOS
            print("在macOS上，建议手动安装中文字体，例如：")
            print("1. 从 https://www.wfonts.com/font/simhei 下载 SimHei 字体")
            print("2. 双击字体文件安装")
            print("3. 重新运行此脚本")
            return False
        elif system == 'Linux':
            try:
                print("尝试在Linux上安装字体...")
                subprocess.run(['apt-get', 'update'], check=True)
                subprocess.run(['apt-get', 'install', '-y', 'fonts-wqy-zenhei'], check=True)
                # 刷新字体缓存
                subprocess.run(['fc-cache', '-f', '-v'], check=True)
                print("字体安装成功，重新加载字体...")
                fm._rebuild()
                return True
            except Exception as e:
                print(f"安装字体时出错: {e}")
                print("请手动安装中文字体，例如：")
                print("sudo apt-get install fonts-wqy-zenhei")
                return False
        elif system == 'Windows':
            print("Windows系统通常已经安装了中文字体")
            print("如果仍然出现问题，请确保安装了SimSun或Microsoft YaHei字体")
            return True
        else:
            print(f"未知系统: {system}，请手动安装中文字体")
            return False
    
    return True

def get_best_chinese_font():
    """
    获取系统中最适合的中文字体
    """
    # 优先选择的字体列表
    preferred_fonts = ['SimHei', 'Microsoft YaHei', 'PingFang SC', 'STHeiti', 'SimSun', 
                      'WenQuanYi Zen Hei', 'WenQuanYi Micro Hei', 'Source Han Sans CN', 
                      'Noto Sans CJK SC', 'Noto Sans SC', 'Heiti SC', 'Arial Unicode MS']
    
    # 获取系统中所有字体名称
    system_fonts = set([f.name for f in fm.fontManager.ttflist])
    
    # 查找最优字体
    for font in preferred_fonts:
        if font in system_fonts:
            print(f"选择中文字体: {font}")
            return font
    
    # 如果没有找到优先字体，尝试查找任何可能的中文字体
    chinese_keywords = ['Hei', 'hei', 'Kai', 'kai', 'Song', 'song', 'Ming', 'ming', 
                      'Yuan', 'yuan', 'Ping', 'ping', 'Fang', 'fang', 'Yahei']
    
    for font in system_fonts:
        if any(keyword in font for keyword in chinese_keywords):
            print(f"选择中文字体: {font}")
            return font
    
    # 如果找不到任何中文字体，使用默认字体
    print("警告：找不到中文字体，将使用默认字体")
    return 'sans-serif'

def setup_matplotlib_chinese():
    """
    设置Matplotlib以支持中文显示
    """
    # 获取最佳中文字体
    best_font = get_best_chinese_font()
    
    # 设置全局字体
    plt.rcParams['font.sans-serif'] = [best_font] + plt.rcParams['font.sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建字体属性对象
    font_prop = FontProperties(family=best_font)
    
    return font_prop

def test_font(font_prop):
    """
    测试字体是否能正确显示中文，但不输出图片文件
    """
    # 仅打印字体信息，不生成测试图片
    print(f"使用字体: {font_prop.get_name()}")

def setup_chinese_font():
    """
    快速设置中文字体，用于外部导入
    """
    return setup_matplotlib_chinese()

def setup_chinese_fonts():
    """
    设置中文字体并返回FontProperties对象
    该函数是为了兼容性添加的，功能与setup_chinese_font相同
    """
    # 检查是否需要安装字体
    if not install_font_if_needed():
        print("警告：中文字体设置可能不完整")
    
    # 设置Matplotlib的中文字体
    font_prop = setup_matplotlib_chinese()
    
    # 设置Seaborn风格（可选）
    try:
        sns.set(font=font_prop.get_name(), font_scale=1.1)
    except:
        print("注意：Seaborn字体设置失败，但Matplotlib应该正常工作")
    
    return font_prop
