from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)  # 创建行情对象
ret, data = quote_ctx.get_market_snapshot('HK.00700')
if ret == RET_OK:
    data.to_json('snapshot.json', orient='records', force_ascii=False, indent=4)
    print('快照数据已保存到 snapshot.json')
else:
    print('error:', data)
quote_ctx.close() # 关闭对象，防止连接条数用尽


trd_ctx = OpenSecTradeContext(host='127.0.0.1', port=11111)  # 创建交易对象
print(trd_ctx.place_order(price=500.0, qty=100, code="HK.00700", trd_side=TrdSide.BUY, trd_env=TrdEnv.SIMULATE))  # 模拟交易，下单（如果是真实环境交易，在此之前需要先解锁交易密码）

trd_ctx.close()  # 关闭对象，防止连接条数用尽
