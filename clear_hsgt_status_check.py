#!/usr/bin/env python3
"""
清晰的港股通数据状态检查

解决之前测试脚本中的逻辑混乱问题，提供清晰准确的港股通数据状态报告。
"""

import pandas as pd
import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, '/Users/<USER>/AI/Cursor/Futu')

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    print("❌ AKShare not available")
    exit(1)

def check_api_hsgt_data():
    """检查API中的港股通数据"""
    
    print("📡 Checking API HSGT Data")
    print("=" * 30)
    
    try:
        # 获取港股通数据
        hsgt_data = ak.stock_hsgt_individual_em(symbol="00001")
        
        if hsgt_data is None or hsgt_data.empty:
            print("❌ No HSGT data from API")
            return None
        
        print(f"✅ API returned {len(hsgt_data)} HSGT records")
        
        # 转换日期为字符串格式便于比较
        hsgt_data['date_str'] = hsgt_data['持股日期'].astype(str)
        
        # 显示最新10条记录
        print(f"\n📅 Latest 10 API records:")
        latest_10 = hsgt_data.sort_values('持股日期', ascending=False).head(10)
        
        for i, (_, row) in enumerate(latest_10.iterrows()):
            date_str = row['date_str']
            shares = row['持股数量']
            value = row['持股市值']
            print(f"  {i+1:2d}. {date_str} | {shares:>12,.0f} shares | {value:>15,.0f} HKD")
        
        # 检查特定日期
        target_dates = ['2025-07-30', '2025-07-31', '2025-08-01', '2025-08-02']
        
        print(f"\n🎯 Checking specific dates:")
        for target_date in target_dates:
            if target_date in hsgt_data['date_str'].values:
                target_row = hsgt_data[hsgt_data['date_str'] == target_date].iloc[0]
                print(f"  ✅ {target_date}: {target_row['持股数量']:,.0f} shares")
            else:
                print(f"  ❌ {target_date}: Not available")
        
        return hsgt_data
        
    except Exception as e:
        print(f"❌ API error: {e}")
        return None

def check_csv_hsgt_data():
    """检查CSV文件中的港股通数据"""
    
    print(f"\n📁 Checking CSV File HSGT Data")
    print("=" * 35)
    
    csv_file = "/Users/<USER>/AI/Cursor/Futu/data/H_daily/00001.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found: {csv_file}")
        return None
    
    try:
        # 读取CSV数据
        csv_data = pd.read_csv(csv_file)
        print(f"✅ CSV loaded: {len(csv_data)} total records")
        
        # 检查港股通数据
        hsgt_records = csv_data[csv_data['hsgt_date'].notna()]
        print(f"📊 HSGT records in CSV: {len(hsgt_records)}")
        
        if len(hsgt_records) > 0:
            # 显示最新10条港股通记录
            print(f"\n📅 Latest 10 CSV HSGT records:")
            latest_10_csv = hsgt_records.tail(10)
            
            for i, (_, row) in enumerate(latest_10_csv.iterrows()):
                date = row['hsgt_date']
                shares = row['hsgt_holding_shares']
                value = row['hsgt_holding_value']
                print(f"  {i+1:2d}. {date} | {shares:>12,.0f} shares | {value:>15,.0f} HKD")
            
            # 检查特定日期
            target_dates = ['2025-07-30', '2025-07-31', '2025-08-01', '2025-08-02']
            
            print(f"\n🎯 Checking specific dates in CSV:")
            for target_date in target_dates:
                target_rows = csv_data[csv_data['hsgt_date'] == target_date]
                if not target_rows.empty:
                    target_row = target_rows.iloc[0]
                    print(f"  ✅ {target_date}: {target_row['hsgt_holding_shares']:,.0f} shares")
                else:
                    print(f"  ❌ {target_date}: Not available")
        else:
            print("❌ No HSGT data found in CSV")
        
        return csv_data
        
    except Exception as e:
        print(f"❌ CSV error: {e}")
        return None

def compare_api_vs_csv(api_data, csv_data):
    """比较API数据和CSV数据"""
    
    print(f"\n🔄 Comparing API vs CSV Data")
    print("=" * 35)
    
    if api_data is None or csv_data is None:
        print("❌ Cannot compare - missing data")
        return
    
    # 准备比较数据
    api_data['date_str'] = api_data['持股日期'].astype(str)
    csv_hsgt = csv_data[csv_data['hsgt_date'].notna()]
    
    print(f"API records: {len(api_data)}")
    print(f"CSV HSGT records: {len(csv_hsgt)}")
    
    # 检查最新几个日期的一致性
    target_dates = ['2025-07-30', '2025-07-31', '2025-08-01']
    
    print(f"\n📊 Data consistency check:")
    print(f"{'Date':<12} {'API':<15} {'CSV':<15} {'Status':<10}")
    print("-" * 55)
    
    for target_date in target_dates:
        # API数据
        api_has_data = target_date in api_data['date_str'].values
        api_shares = "N/A"
        if api_has_data:
            api_row = api_data[api_data['date_str'] == target_date].iloc[0]
            api_shares = f"{api_row['持股数量']:,.0f}"
        
        # CSV数据
        csv_has_data = target_date in csv_data['hsgt_date'].values
        csv_shares = "N/A"
        if csv_has_data:
            csv_row = csv_data[csv_data['hsgt_date'] == target_date].iloc[0]
            csv_shares = f"{csv_row['hsgt_holding_shares']:,.0f}"
        
        # 状态判断
        if api_has_data and csv_has_data:
            if api_shares == csv_shares:
                status = "✅ Match"
            else:
                status = "⚠️  Differ"
        elif api_has_data and not csv_has_data:
            status = "🔄 Need Update"
        elif not api_has_data and csv_has_data:
            status = "🤔 CSV Newer"
        else:
            status = "❌ Both Missing"
        
        print(f"{target_date:<12} {api_shares:<15} {csv_shares:<15} {status:<10}")

def identify_missing_dates(api_data, csv_data):
    """识别需要更新的日期"""
    
    print(f"\n🎯 Identifying Missing Dates")
    print("=" * 30)
    
    if api_data is None or csv_data is None:
        print("❌ Cannot identify - missing data")
        return []
    
    # 准备数据
    api_data['date_str'] = api_data['持股日期'].astype(str)
    api_dates = set(api_data['date_str'].tolist())
    
    csv_hsgt_dates = set(csv_data[csv_data['hsgt_date'].notna()]['hsgt_date'].tolist())
    
    # 找出API有但CSV没有的日期
    missing_in_csv = api_dates - csv_hsgt_dates
    
    # 找出CSV有但API没有的日期  
    extra_in_csv = csv_hsgt_dates - api_dates
    
    print(f"API dates: {len(api_dates)}")
    print(f"CSV HSGT dates: {len(csv_hsgt_dates)}")
    
    if missing_in_csv:
        print(f"\n🔄 Dates in API but missing in CSV ({len(missing_in_csv)}):")
        sorted_missing = sorted(missing_in_csv, reverse=True)
        for date in sorted_missing[:10]:  # 显示最新的10个
            api_row = api_data[api_data['date_str'] == date].iloc[0]
            print(f"  📅 {date}: {api_row['持股数量']:,.0f} shares")
        if len(sorted_missing) > 10:
            print(f"  ... and {len(sorted_missing) - 10} more")
    else:
        print(f"✅ No missing dates in CSV")
    
    if extra_in_csv:
        print(f"\n🤔 Dates in CSV but not in API ({len(extra_in_csv)}):")
        for date in sorted(extra_in_csv, reverse=True)[:5]:
            print(f"  📅 {date}")
    
    return list(missing_in_csv)

def main():
    """主函数"""
    print("🔍 Clear HSGT Status Check")
    print("=" * 30)
    print(f"Check time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查API数据
    api_data = check_api_hsgt_data()
    
    # 2. 检查CSV数据
    csv_data = check_csv_hsgt_data()
    
    # 3. 比较数据
    compare_api_vs_csv(api_data, csv_data)
    
    # 4. 识别缺失日期
    missing_dates = identify_missing_dates(api_data, csv_data)
    
    print(f"\n🏁 Summary")
    print("=" * 15)
    
    if api_data is not None:
        latest_api_date = api_data['date_str'].max()
        print(f"📡 Latest API date: {latest_api_date}")
    
    if csv_data is not None:
        csv_hsgt = csv_data[csv_data['hsgt_date'].notna()]
        if len(csv_hsgt) > 0:
            latest_csv_date = csv_hsgt['hsgt_date'].max()
            print(f"📁 Latest CSV date: {latest_csv_date}")
        else:
            print(f"📁 CSV has no HSGT data")
    
    if missing_dates:
        print(f"🔄 Missing dates to update: {len(missing_dates)}")
        print(f"   Most recent: {sorted(missing_dates, reverse=True)[:3]}")
    else:
        print(f"✅ CSV is up to date with API")
    
    # 解释之前的混乱
    print(f"\n💡 Explanation of previous confusion:")
    print("The test script had logic errors in date format handling.")
    print("This clear check shows the actual data status.")

if __name__ == "__main__":
    main()
