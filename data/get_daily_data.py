import os
import sys
import pandas as pd
from futu import *
import datetime
import time
from multiprocessing import Pool, cpu_count
from functools import partial
from tqdm import tqdm
import random

def get_all_history_kline(quote_ctx, code, max_retries=3):
    """
    获取单只股票的所有历史K线数据，带重试机制
    """
    all_data = pd.DataFrame()
    page_req_key = None

    # 从一个很早的日期开始，确保能覆盖上市日期
    start_date = '1990-01-01'
    end_date = datetime.datetime.now().strftime('%Y-%m-%d')

    while True:
        retry_count = 0
        while retry_count < max_retries:
            try:
                # 添加随机延迟，避免频率限制
                time.sleep(random.uniform(0.5, 1.5))

                ret, data, page_req_key = quote_ctx.request_history_kline(
                    code,
                    start=start_date,
                    end=end_date,
                    ktype=KLType.K_DAY,
                    autype=AuType.HFQ,
                    max_count=1000,
                    page_req_key=page_req_key
                )

                if ret == RET_OK:
                    if data is not None and not data.empty:
                        all_data = pd.concat([all_data, data], ignore_index=True)
                    else:
                        return all_data  # 没有更多数据
                    break  # 成功，跳出重试循环
                else:
                    if "频率太高" in str(data) or "frequency" in str(data).lower():
                        # 频率限制，等待更长时间后重试
                        wait_time = (retry_count + 1) * 5 + random.uniform(1, 3)
                        print(f"Rate limit hit for {code}, waiting {wait_time:.1f}s before retry {retry_count + 1}/{max_retries}")
                        time.sleep(wait_time)
                        retry_count += 1
                    else:
                        print(f"Error getting kline for {code}: {data}")
                        return None

            except Exception as e:
                if "频率太高" in str(e) or "frequency" in str(e).lower():
                    wait_time = (retry_count + 1) * 5 + random.uniform(1, 3)
                    print(f"Rate limit exception for {code}, waiting {wait_time:.1f}s before retry {retry_count + 1}/{max_retries}")
                    time.sleep(wait_time)
                    retry_count += 1
                else:
                    print(f"Exception getting kline for {code}: {e}")
                    return None

        if retry_count >= max_retries:
            print(f"Max retries exceeded for {code}")
            return None

        if page_req_key is None:
            break

    return all_data

def process_single_stock(stock_info):
    """
    处理单只股票的下载任务
    """
    stock_code, output_dir, today = stock_info

    # 每个进程创建自己的连接，添加随机延迟避免同时连接
    quote_ctx = None
    try:
        # 添加随机延迟，避免同时创建连接
        time.sleep(random.uniform(0.1, 0.5))
        quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

        futu_code = f"HK.{stock_code}"
        output_path = os.path.join(output_dir, f"{stock_code}.csv")

        # 检查是否需要更新
        if os.path.exists(output_path):
            try:
                df_last = pd.read_csv(output_path, usecols=['time_key']).tail(1)
                if not df_last.empty:
                    last_date = df_last['time_key'].iloc[0].split(' ')[0]
                    if last_date >= today:
                        return f"{futu_code}: Data is up-to-date, skipped"
            except Exception:
                pass  # 如果检查失败，继续下载

        # 获取历史数据
        history_data = get_all_history_kline(quote_ctx, futu_code)

        if history_data is not None and not history_data.empty:
            # 数据清理
            history_data.drop_duplicates(subset='time_key', keep='last', inplace=True)
            history_data.sort_values(by='time_key', inplace=True)

            # 保存数据
            history_data.to_csv(output_path, index=False)
            return f"{futu_code}: Saved {len(history_data)} records"
        else:
            return f"{futu_code}: No data retrieved"

    except Exception as e:
        return f"{stock_code}: Error - {str(e)}"
    finally:
        if quote_ctx:
            quote_ctx.close()

def consolidate_to_parquet(csv_dir, output_path):
    """
    将CSV文件整合为Parquet
    """
    import glob
    
    csv_files = glob.glob(os.path.join(csv_dir, '*.csv'))
    if not csv_files:
        print(f"No CSV files found in {csv_dir}")
        return
    
    print(f"Consolidating {len(csv_files)} CSV files...")
    all_dataframes = []
    
    for csv_file in tqdm(csv_files, desc="Reading CSV files"):
        try:
            stock_code = os.path.basename(csv_file).replace('.csv', '')
            df = pd.read_csv(csv_file)
            
            if df.empty:
                continue
                
            df['stock_code'] = stock_code
            df['time_key'] = pd.to_datetime(df['time_key'])
            all_dataframes.append(df)
            
        except Exception as e:
            print(f"Error processing {csv_file}: {e}")
            continue
    
    if all_dataframes:
        print("Merging all data...")
        consolidated_df = pd.concat(all_dataframes, ignore_index=True)
        consolidated_df.sort_values(['stock_code', 'time_key'], inplace=True)
        
        print("Saving to Parquet...")
        consolidated_df.to_parquet(output_path, index=False, compression='snappy')
        print(f"✅ Consolidated data saved to {output_path}")
        print(f"Total records: {len(consolidated_df):,}, Stocks: {consolidated_df['stock_code'].nunique()}")

def main():
    """
    主函数
    """
    # 获取脚本绝对路径，确保路径正确
    script_path = os.path.abspath(__file__)
    script_dir = os.path.dirname(script_path)
    project_root = os.path.dirname(script_dir)
    
    print(f"Script path: {script_path}")
    print(f"Project root: {project_root}")
    
    # 定义所有路径
    stock_list_path = os.path.join(project_root, 'data', 'H_list')
    output_dir = os.path.join(project_root, 'data', 'H_daily')
    parquet_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')
    
    print(f"Stock list path: {stock_list_path}")
    print(f"Output directory: {output_dir}")
    
    # 检查股票列表文件是否存在
    if not os.path.exists(stock_list_path):
        print(f"❌ Error: Stock list file not found at {stock_list_path}")
        print("Please ensure the H_list file exists in the correct location.")
        return
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取股票代码
    try:
        with open(stock_list_path, 'r', encoding='utf-8') as f:
            stock_codes = [line.strip() for line in f if line.strip()]
        print(f"✅ Loaded {len(stock_codes)} stock codes")
    except Exception as e:
        print(f"❌ Error reading stock list: {e}")
        return
    
    # 获取当前日期作为参考
    today = datetime.datetime.now().strftime('%Y-%m-%d')
    print(f"Reference date: {today}")
    
    # 准备多进程任务 - 大幅降低并发数以避免频率限制
    # 富途API每30秒最多60次请求，考虑到每只股票可能需要多次请求，使用更保守的并发数
    num_processes = min(cpu_count(), 2)  # 最多使用2个进程，避免API频率限制
    print(f"🚀 Using {num_processes} processes for parallel downloading...")
    print("⚠️  Using conservative concurrency to avoid API rate limits")
    
    # 创建任务列表
    tasks = [(stock_code, output_dir, today) for stock_code in stock_codes]
    
    # 使用多进程下载
    with Pool(num_processes) as pool:
        results = list(tqdm(
            pool.imap(process_single_stock, tasks),
            total=len(tasks),
            desc="Downloading stocks"
        ))
    
    # 统计结果
    success_count = sum(1 for result in results if "Saved" in result or "up-to-date" in result)
    error_count = sum(1 for result in results if "Error" in result)
    
    print(f"\n✅ Download completed!")
    print(f"Successfully processed: {success_count}/{len(stock_codes)} stocks")
    print(f"Errors: {error_count}")
    
    # 显示部分结果
    print("\nSample results:")
    for result in results[:5]:
        print(f"  {result}")
    if len(results) > 5:
        print(f"  ... and {len(results) - 5} more")
    
    # 整合为Parquet文件
    print("\n" + "="*50)
    print("Starting consolidation to Parquet...")
    consolidate_to_parquet(output_dir, parquet_path)
    print("="*50)
    print("🎉 All tasks completed!")

if __name__ == "__main__":
    main() 